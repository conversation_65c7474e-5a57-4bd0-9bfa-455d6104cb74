#!/bin/bash

# StockTrek Installation Script
# Ensures everything works with live data and up-to-date dependencies

echo "🚀 StockTrek Installation & Setup Script"
echo "========================================"

# Check if we're in the right directory
if [ ! -f "main.py" ]; then
    echo "❌ Error: Please run this script from the StockTrek directory"
    exit 1
fi

# Remove existing venv if it exists and is broken
if [ -d "venv" ]; then
    echo "🗑️ Removing existing virtual environment..."
    rm -rf venv
fi

# Create fresh virtual environment
echo "🐍 Creating fresh Python virtual environment..."
python3 -m venv venv

# Check if venv creation was successful
if [ ! -d "venv" ]; then
    echo "❌ Failed to create virtual environment"
    exit 1
fi

# Activate virtual environment
echo "✅ Activating virtual environment..."
source venv/bin/activate

# Upgrade pip in the virtual environment
echo "📦 Upgrading pip..."
python -m pip install --upgrade pip

# Install wheel and setuptools first
echo "🔧 Installing build tools..."
pip install wheel setuptools

# Install requirements
echo "📋 Installing requirements..."
pip install -r requirements.txt

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p models logs data backups

# Test imports
echo "🧪 Testing imports..."
python -c "
import sys
print(f'Python version: {sys.version}')

try:
    import yfinance as yf
    print('✅ yfinance imported successfully')
except ImportError as e:
    print(f'❌ yfinance import failed: {e}')
    sys.exit(1)

try:
    import pandas as pd
    print('✅ pandas imported successfully')
except ImportError as e:
    print(f'❌ pandas import failed: {e}')
    sys.exit(1)

try:
    import numpy as np
    print('✅ numpy imported successfully')
except ImportError as e:
    print(f'❌ numpy import failed: {e}')
    sys.exit(1)

try:
    import sklearn
    print('✅ scikit-learn imported successfully')
except ImportError as e:
    print(f'❌ scikit-learn import failed: {e}')
    sys.exit(1)

print('✅ All core dependencies imported successfully')
"

if [ $? -ne 0 ]; then
    echo "❌ Import test failed"
    exit 1
fi

# Test live data fetch
echo "🌐 Testing live data fetch..."
python -c "
import yfinance as yf
import pandas as pd
from datetime import datetime

print('Testing live data fetch for AAPL...')
try:
    ticker = yf.Ticker('AAPL')
    hist = ticker.history(period='5d')
    info = ticker.info
    
    if hist.empty:
        print('❌ No historical data received')
        exit(1)
    
    print(f'✅ Received {len(hist)} days of historical data')
    print(f'✅ Latest close price: \${hist[\"Close\"].iloc[-1]:.2f}')
    print(f'✅ Company name: {info.get(\"longName\", \"N/A\")}')
    print('✅ Live data fetch successful')
    
except Exception as e:
    print(f'❌ Live data fetch failed: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ Live data test failed"
    exit 1
fi

echo ""
echo "🎉 Installation completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Activate the environment: source venv/bin/activate"
echo "2. Test the system: python test_live_system.py"
echo "3. Make a prediction: python main.py --predict AAPL --timeframe 30"
echo ""
echo "💡 The virtual environment is now ready with all dependencies!"
