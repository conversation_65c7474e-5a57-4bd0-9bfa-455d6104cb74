# StockTrek - Production Stock Prediction System

🚀 **Advanced Neural Network Stock Prediction System with 97.5% Claimed Accuracy**

StockTrek is a production-ready stock prediction system that combines financial data analysis, sentiment analysis, and neural network predictions with autonomous operation capabilities.

## 🌟 Features

- **Neural Network Predictions**: Advanced ML models with timeframe-aware predictions
- **Autonomous Daemon Mode**: 24/7 operation with self-learning capabilities
- **Comprehensive Backtesting**: Validate predictions against actual outcomes
- **Real-time Data Processing**: ETL pipeline for financial and sentiment data
- **Production-Ready**: Modular architecture with comprehensive logging
- **CLI Interface**: Easy-to-use command-line interface
- **Database Integration**: PostgreSQL for data persistence

## 🏗️ Architecture

```
StockTrek/
├── main.py                 # Main CLI application
├── src/                    # Core modules
│   ├── neural_network.py   # Neural network predictor
│   ├── data_manager.py     # Database operations
│   ├── daemon.py           # Autonomous operation
│   └── backtesting.py      # Prediction validation
├── ETL/                    # Data processing
├── Preprocessing/          # Data preprocessing
├── Databases/              # Database schemas
└── models/                 # Trained models
```

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd StockTrek

# Run setup script
python3 setup.py
```

### 2. Database Setup

Set up PostgreSQL database:

```sql
CREATE DATABASE stocktrek_mainbranch;
CREATE USER stocktrek_admin WITH PASSWORD 'equity_FR';
GRANT ALL PRIVILEGES ON DATABASE stocktrek_mainbranch TO stocktrek_admin;
```

### 3. Basic Usage

```bash
# Make a prediction
python3 main.py --predict AAPL --timeframe 30

# Run backtest
python3 main.py --backtest TSLA --days 90

# Start daemon mode
python3 main.py --daemon

# Train model
python3 main.py --train
```

## 📊 Usage Examples

### Stock Prediction

```bash
# Predict Apple stock for 30 days
python3 main.py --predict AAPL --timeframe 30

# Predict Tesla for 60 days
python3 main.py --predict TSLA --timeframe 60
```

**Sample Output:**
```
============================================================
🎯 STOCKTREK PREDICTION RESULTS
============================================================
📈 Ticker: AAPL
⏰ Timeframe: 30 days
📅 Analysis Date: 2024-12-30 10:30:00
------------------------------------------------------------
🚀 Prediction: BUY
🎯 Confidence: 87.3%
💰 Current Price: $195.50
🎯 Target Price: $210.25
📊 Expected Return: 7.5%
🤖 Model Certainty: High
============================================================
```

### Backtesting

```bash
# Run backtest for Microsoft over 90 days
python3 main.py --backtest MSFT --days 90
```

**Sample Output:**
```
============================================================
🧪 BACKTEST RESULTS
============================================================
📈 Ticker: MSFT
📅 Period: 90 days
------------------------------------------------------------
🎯 Accuracy: 89.2%
📊 Total Predictions: 65
✅ Correct Predictions: 58
❌ Incorrect Predictions: 7
📈 Average Return: 2.3%
📉 Max Drawdown: -1.8%
============================================================
```

### Daemon Mode

```bash
# Start autonomous operation
python3 main.py --daemon
```

The daemon will:
- Make autonomous predictions every hour
- Run backtests every 24 hours
- Update data every 30 minutes
- Self-learn and retrain when accuracy drops

## 🧠 Neural Network Features

### Comprehensive Feature Extraction

- **Technical Indicators**: RSI, MACD, Bollinger Bands, Moving Averages
- **Fundamental Analysis**: P/E ratios, Revenue growth, Profit margins
- **Timeframe Features**: Timeframe-aware volatility and momentum
- **Market Sentiment**: News sentiment analysis integration

### Model Architecture

- **Ensemble Learning**: Random Forest + Neural Network
- **Feature Scaling**: StandardScaler for optimal performance
- **Caching**: 5-minute prediction cache for efficiency
- **Fallback System**: Rule-based predictions when ML unavailable

## 🗄️ Database Schema

### Core Tables

- **predictions**: Store prediction results and outcomes
- **backtest_results**: Store backtesting performance metrics
- **model_performance**: Track model accuracy over time
- **companies**: Company information and financial data

### Data Flow

1. **ETL Pipeline**: Fetch and process financial/sentiment data
2. **Feature Engineering**: Extract comprehensive features
3. **Prediction**: Generate ML-based predictions
4. **Storage**: Store results in PostgreSQL
5. **Validation**: Backtest against actual outcomes

## 🔧 Configuration

### Daemon Configuration

```python
config = {
    'prediction_interval': 3600,    # 1 hour
    'backtest_interval': 86400,     # 24 hours
    'data_update_interval': 1800,   # 30 minutes
    'watchlist': ['AAPL', 'MSFT', 'GOOGL', 'TSLA'],
    'timeframes': [7, 14, 30, 60, 90],
    'learning_threshold': 0.7       # Retrain below 70% accuracy
}
```

### Database Configuration

```python
db_config = {
    'dbname': 'stocktrek_mainbranch',
    'user': 'stocktrek_admin',
    'password': 'equity_FR',
    'host': 'localhost',
    'port': '5432'
}
```

## 📈 Performance Metrics

- **Claimed Accuracy**: 97.5% (from original neural network)
- **Backtesting**: Comprehensive validation against historical data
- **Self-Learning**: Automatic retraining when performance drops
- **Real-time**: Sub-second prediction generation with caching

## 🛠️ Development

### Running Tests

```bash
# Run integration tests
python3 test_stocktrek.py

# Test specific components
python3 -c "from src.neural_network import NeuralNetworkPredictor; print('✅ Neural network works')"
```

### Adding New Features

1. **New Indicators**: Add to `_calculate_technical_indicators()`
2. **New Models**: Extend `NeuralNetworkPredictor` class
3. **New Data Sources**: Extend ETL pipeline
4. **New Timeframes**: Update daemon configuration

## 📋 Dependencies

### Core Requirements

- Python 3.8+
- PostgreSQL 12+
- yfinance >= 0.2.63
- scikit-learn >= 1.3.0
- pandas >= 2.0.0
- psycopg2-binary >= 2.9.0

### Full Requirements

See `requirements.txt` for complete dependency list.

## 🚨 Important Notes

1. **Free Resources Only**: System designed to use only free APIs and resources
2. **Production Ready**: Comprehensive error handling and logging
3. **Modular Design**: Easy to extend and modify
4. **No Unnecessary Files**: Clean, production-focused codebase
5. **Database Required**: PostgreSQL setup required for full functionality

## 📞 Support

For issues or questions:
1. Check logs in `stocktrek.log`
2. Run `python3 test_stocktrek.py` for diagnostics
3. Verify database connection and credentials
4. Ensure all dependencies are installed

## ⚖️ Disclaimer

This system is for educational and research purposes. Past performance does not guarantee future results. Always do your own research before making investment decisions.

---

**StockTrek** - Advanced Stock Prediction with Neural Networks 🚀
