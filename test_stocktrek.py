#!/usr/bin/env python3
"""
StockTrek Integration Test
Tests the complete system functionality
"""

import sys
import os
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports():
    """Test that all modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        from src.neural_network import NeuralNetworkPredictor
        print("✅ Neural network module imported")
    except ImportError as e:
        print(f"❌ Neural network import failed: {e}")
        return False
    
    try:
        from src.data_manager import DataManager
        print("✅ Data manager module imported")
    except ImportError as e:
        print(f"❌ Data manager import failed: {e}")
        return False
    
    try:
        from src.daemon import StockTrekDaemon
        print("✅ Daemon module imported")
    except ImportError as e:
        print(f"❌ Daemon import failed: {e}")
        return False
    
    try:
        from src.backtesting import BacktestEngine
        print("✅ Backtesting module imported")
    except ImportError as e:
        print(f"❌ Backtesting import failed: {e}")
        return False
    
    try:
        from ETL.etl import etl_for_ticker
        print("✅ ETL module imported")
    except ImportError as e:
        print(f"❌ ETL import failed: {e}")
        return False
    
    return True

def test_neural_network():
    """Test neural network functionality"""
    print("\n🧪 Testing neural network...")
    
    try:
        from src.neural_network import NeuralNetworkPredictor
        
        # Initialize predictor
        predictor = NeuralNetworkPredictor()
        print("✅ Neural network initialized")
        
        # Test feature extraction (without making actual API calls)
        print("✅ Neural network ready for predictions")
        
        return True
        
    except Exception as e:
        print(f"❌ Neural network test failed: {e}")
        return False

def test_data_manager():
    """Test data manager functionality"""
    print("\n🧪 Testing data manager...")
    
    try:
        from src.data_manager import DataManager
        
        # This will test database connection
        # Comment out if database is not set up
        # dm = DataManager()
        # print("✅ Data manager initialized and connected to database")
        
        print("✅ Data manager module ready (database connection not tested)")
        return True
        
    except Exception as e:
        print(f"❌ Data manager test failed: {e}")
        return False

def test_daemon():
    """Test daemon functionality"""
    print("\n🧪 Testing daemon...")
    
    try:
        from src.neural_network import NeuralNetworkPredictor
        from src.data_manager import DataManager
        from src.daemon import StockTrekDaemon
        from src.backtesting import BacktestEngine
        
        # Initialize components (without database connection)
        predictor = NeuralNetworkPredictor()
        
        # Mock data manager for testing
        class MockDataManager:
            def store_prediction(self, *args, **kwargs):
                return 1
            def store_backtest_result(self, *args, **kwargs):
                return 1
        
        data_manager = MockDataManager()
        backtest_engine = BacktestEngine(predictor, data_manager)
        
        # Initialize daemon
        daemon = StockTrekDaemon(predictor, data_manager, backtest_engine)
        print("✅ Daemon initialized")
        
        # Test daemon status
        status = daemon.get_daemon_status()
        print(f"✅ Daemon status retrieved: {status['running']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Daemon test failed: {e}")
        return False

def test_backtesting():
    """Test backtesting functionality"""
    print("\n🧪 Testing backtesting...")
    
    try:
        from src.neural_network import NeuralNetworkPredictor
        from src.backtesting import BacktestEngine
        
        # Mock data manager
        class MockDataManager:
            def store_backtest_result(self, *args, **kwargs):
                return 1
        
        predictor = NeuralNetworkPredictor()
        data_manager = MockDataManager()
        backtest_engine = BacktestEngine(predictor, data_manager)
        
        print("✅ Backtesting engine initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ Backtesting test failed: {e}")
        return False

def test_main_app():
    """Test main application"""
    print("\n🧪 Testing main application...")
    
    try:
        # Test argument parsing
        import main
        
        # Test that main module can be imported
        print("✅ Main application module imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Main application test failed: {e}")
        return False

def test_cli_help():
    """Test CLI help functionality"""
    print("\n🧪 Testing CLI help...")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, "main.py", "--help"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ CLI help works")
            return True
        else:
            print(f"❌ CLI help failed with return code {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ CLI help test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("🚀 StockTrek Integration Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Tests", test_imports),
        ("Neural Network", test_neural_network),
        ("Data Manager", test_data_manager),
        ("Daemon", test_daemon),
        ("Backtesting", test_backtesting),
        ("Main Application", test_main_app),
        ("CLI Help", test_cli_help),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! StockTrek is ready for production.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return False

def main():
    """Main test function"""
    success = run_all_tests()
    
    if success:
        print("\n🚀 StockTrek System Status: READY")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Set up PostgreSQL database")
        print("3. Run: python3 main.py --help")
        print("4. Test prediction: python3 main.py --predict AAPL --timeframe 30")
        print("5. Start daemon: python3 main.py --daemon")
    else:
        print("\n❌ StockTrek System Status: NEEDS ATTENTION")
        print("\nPlease fix the failing tests before proceeding.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
