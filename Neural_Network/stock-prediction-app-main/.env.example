# API Keys for data collection
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
FINNHUB_API_KEY=your_finnhub_key_here
NEWS_API_KEY=your_news_api_key_here

# Optional: LLM API keys if using external services
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here

# Database configuration
DATABASE_URL=sqlite:///data/stock_prediction.db

# Model configuration
MODEL_CACHE_DIR=./models/cache
HUGGINGFACE_CACHE_DIR=./models/huggingface_cache

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/stock_prediction.log

# Data collection settings
MAX_RETRIES=3
REQUEST_DELAY=1.0

# Training settings
DEVICE=cuda  # or cpu
MIXED_PRECISION=true
GRADIENT_CHECKPOINTING=true

# Flask Authentication Configuration
SECRET_KEY=your-super-secret-key-here
FLASK_ENV=production

# Email Configuration (SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key-here
MAIL_DEFAULT_SENDER=<EMAIL>

# Security Settings
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax
