# Web framework
Flask>=2.3.0
Flask-CORS>=4.0.0
Flask-SQLAlchemy>=3.0.0
Flask-Migrate>=4.0.0
Flask-Login>=0.6.0
Flask-Mail>=0.9.1

# Data manipulation and analysis
pandas>=2.0.0
numpy>=1.24.0

# Financial data
yfinance>=0.2.18
alpha-vantage>=2.3.1
polygon-api-client>=1.12.0
requests>=2.31.0

# Configuration and utilities
python-dotenv>=1.0.0

# Authentication and security
bcrypt>=4.0.0
PyJWT>=2.8.0

# Email services
sendgrid>=6.10.0

# Production server
gunicorn>=21.2.0

# Machine Learning and Neural Networks
# tensorflow>=2.13.0  # Removed - not compatible with Python 3.13, production uses scikit-learn
scikit-learn>=1.3.0
joblib>=1.3.0

# Optional: For future LLM integration
# torch>=2.0.0
# transformers>=4.30.0
# openai>=0.27.0
