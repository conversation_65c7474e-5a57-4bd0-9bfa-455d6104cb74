<!DOCTYPE html>
<html>
<head>
    <title>Frontend Fix Test</title>
</head>
<body>
    <h1>Testing JavaScript Fix</h1>
    <div id="test-results"></div>

    <script>
        // Mock stockDatabase for testing
        const stockDatabase = {
            'AAPL': { name: 'Apple Inc.', price: 201.50, sector: 'Technology', marketCap: '2.9T', volume: 45000000, pe: 31.2, beta: 1.25 },
            'GOOGL': { name: 'Alphabet Inc.', price: 166.01, sector: 'Technology', marketCap: '1.7T', volume: 28000000, pe: 24.8, beta: 1.15 }
        };

        // Test function similar to the one in index.html
        function generateAIReasoning(data) {
            console.log('Testing generateAIReasoning with data:', data);
            
            try {
                // This is the line that was causing the error
                const pe = data?.pe || stockDatabase[data.symbol]?.pe || 25;
                console.log('P/E ratio calculated:', pe);
                
                return `
                    <div class="reasoning-item">
                        <div class="reasoning-text">
                            <strong>Test Result:</strong> Successfully calculated P/E ratio: ${pe}
                        </div>
                    </div>
                `;
            } catch (error) {
                console.error('Error in generateAIReasoning:', error);
                return `<div class="error">Error: ${error.message}</div>`;
            }
        }

        // Test cases
        function runTests() {
            const testResults = document.getElementById('test-results');
            let results = '<h2>Test Results:</h2>';

            // Test 1: Data with P/E ratio
            console.log('Test 1: Data with P/E ratio');
            const test1Data = { symbol: 'AAPL', pe: 30.5 };
            const test1Result = generateAIReasoning(test1Data);
            results += `<h3>Test 1 - Data with P/E:</h3>${test1Result}`;

            // Test 2: Data without P/E, but symbol in database
            console.log('Test 2: Data without P/E, but symbol in database');
            const test2Data = { symbol: 'GOOGL' };
            const test2Result = generateAIReasoning(test2Data);
            results += `<h3>Test 2 - Fallback to database:</h3>${test2Result}`;

            // Test 3: Data without P/E and symbol not in database
            console.log('Test 3: Data without P/E and symbol not in database');
            const test3Data = { symbol: 'UNKNOWN' };
            const test3Result = generateAIReasoning(test3Data);
            results += `<h3>Test 3 - Default fallback:</h3>${test3Result}`;

            testResults.innerHTML = results;
            console.log('All tests completed successfully!');
        }

        // Run tests when page loads
        window.onload = runTests;
    </script>
</body>
</html>
