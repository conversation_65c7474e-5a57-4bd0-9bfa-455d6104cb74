#!/usr/bin/env python3
"""
StockTrek Neural Network - Production-Ready Self-Learning System
ONE unified model that trains with every call using live data.
"""

import os
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import yfinance as yf
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.ensemble import VotingClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.impute import SimpleImputer
from sklearn.model_selection import train_test_split
import joblib
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class NeuralNetworkPredictor:
    """
    Production-ready self-learning neural network.
    ONE model that's always available and trains with every call.
    """

    def __init__(self):
        """Initialize the unified neural network system"""
        logger.info("🚀 Initializing Production Neural Network System")

        # Setup directories
        self.models_dir = "models"
        os.makedirs(self.models_dir, exist_ok=True)

        # Initialize the ONE unified model system
        self.model = self._create_initial_model()
        self.scaler = StandardScaler()
        self.imputer = SimpleImputer(strategy='median')
        self.training_data = []
        self.call_count = 0
        self.is_fitted = False

        # Load existing data and model
        self._load_training_data()
        self._initialize_model()

        logger.info(f"✅ Neural Network Ready - Training samples: {len(self.training_data)}, Model fitted: {self.is_fitted}")

    def _create_initial_model(self):
        """Create the unified ensemble model"""
        return VotingClassifier([
            ('rf', RandomForestClassifier(n_estimators=100, max_depth=10, random_state=42)),
            ('gb', GradientBoostingClassifier(n_estimators=100, max_depth=6, random_state=42)),
            ('mlp', MLPClassifier(hidden_layer_sizes=(100, 50), max_iter=500, random_state=42))
        ], voting='soft')

    def _initialize_model(self):
        """Initialize model with existing data if available"""
        if len(self.training_data) >= 10:
            logger.info("🎓 Initializing model with existing training data...")
            self._train_model_with_existing_data()
        else:
            logger.info("📚 Starting fresh - will train with first predictions")

    def predict_stock_movement(self, symbol: str, timeframe_days: int = 30) -> Dict[str, Any]:
        """
        Main prediction method - always uses the ONE unified model.
        Trains and improves with every single call.
        """
        self.call_count += 1
        logger.info(f"🔮 Neural Network Prediction #{self.call_count} for {symbol}")

        try:
            # Get live market data and extract features
            features = self._extract_live_features(symbol)
            if not features:
                return self._emergency_fallback(symbol, timeframe_days)

            # Make prediction with the unified model
            prediction_result = self._make_unified_prediction(features, symbol, timeframe_days)

            # Store data and retrain model with every call
            self._store_and_retrain(symbol, features, prediction_result)

            logger.info(f"✅ Prediction: {prediction_result['prediction']} ({prediction_result['confidence']:.1f}%) | Model: {prediction_result['model_type']}")

            return prediction_result

        except Exception as e:
            logger.error(f"❌ Neural network prediction failed for {symbol}: {e}")
            return self._emergency_fallback(symbol, timeframe_days)

    def _extract_live_features(self, symbol: str) -> Optional[Dict[str, float]]:
        """Extract comprehensive features from live market data"""
        try:
            # Get live stock data
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="1y")  # Get more data for better features
            info = ticker.info

            if len(hist) < 20:
                logger.warning(f"⚠️ Insufficient data for {symbol}")
                return None

            # Calculate comprehensive technical indicators
            close = hist['Close']
            volume = hist['Volume']
            high = hist['High']
            low = hist['Low']

            # Current price information
            current_price = float(close.iloc[-1])

            # Price changes
            price_change_1d = float((close.iloc[-1] - close.iloc[-2]) / close.iloc[-2] * 100) if len(close) >= 2 else 0
            price_change_5d = float((close.iloc[-1] - close.iloc[-6]) / close.iloc[-6] * 100) if len(close) >= 6 else 0
            price_change_20d = float((close.iloc[-1] - close.iloc[-21]) / close.iloc[-21] * 100) if len(close) >= 21 else 0
            price_change_60d = float((close.iloc[-1] - close.iloc[-61]) / close.iloc[-61] * 100) if len(close) >= 61 else 0

            # Moving averages
            ma_5 = float(close.rolling(5).mean().iloc[-1]) if len(close) >= 5 else current_price
            ma_20 = float(close.rolling(20).mean().iloc[-1]) if len(close) >= 20 else current_price
            ma_50 = float(close.rolling(50).mean().iloc[-1]) if len(close) >= 50 else current_price
            ma_200 = float(close.rolling(200).mean().iloc[-1]) if len(close) >= 200 else current_price

            # RSI
            delta = close.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = float(100 - (100 / (1 + rs)).iloc[-1]) if len(gain) >= 14 else 50

            # Bollinger Bands
            bb_middle = close.rolling(20).mean()
            bb_std = close.rolling(20).std()
            bb_upper = bb_middle + (bb_std * 2)
            bb_lower = bb_middle - (bb_std * 2)
            bb_position = float((current_price - bb_lower.iloc[-1]) / (bb_upper.iloc[-1] - bb_lower.iloc[-1])) if len(bb_middle) >= 20 else 0.5

            # Volatility
            volatility = float(close.pct_change().rolling(20).std().iloc[-1] * np.sqrt(252)) if len(close) >= 20 else 0.2

            # Volume analysis
            avg_volume_20 = float(volume.rolling(20).mean().iloc[-1]) if len(volume) >= 20 else float(volume.iloc[-1])
            volume_ratio = float(volume.iloc[-1] / avg_volume_20) if avg_volume_20 > 0 else 1.0

            # Market cap and fundamentals
            market_cap = info.get('marketCap', 0)
            pe_ratio = info.get('trailingPE', 20)

            return {
                'current_price': current_price,
                'price_change_1d': price_change_1d,
                'price_change_5d': price_change_5d,
                'price_change_20d': price_change_20d,
                'price_change_60d': price_change_60d,
                'ma_5': ma_5,
                'ma_20': ma_20,
                'ma_50': ma_50,
                'ma_200': ma_200,
                'rsi': rsi,
                'bb_position': bb_position,
                'volatility': volatility,
                'volume_ratio': volume_ratio,
                'price_to_ma5': (current_price / ma_5 - 1) * 100,
                'price_to_ma20': (current_price / ma_20 - 1) * 100,
                'price_to_ma50': (current_price / ma_50 - 1) * 100,
                'price_to_ma200': (current_price / ma_200 - 1) * 100,
                'market_cap_log': np.log(market_cap) if market_cap > 0 else 20,
                'pe_ratio': pe_ratio if pe_ratio and pe_ratio > 0 else 20
            }

        except Exception as e:
            logger.warning(f"⚠️ Live feature extraction failed for {symbol}: {e}")
            return None

    def _make_unified_prediction(self, features: Dict[str, float], symbol: str, timeframe_days: int) -> Dict[str, Any]:
        """Make prediction using the ONE unified model system"""
        try:
            current_price = features.get('current_price', 0)

            if self.is_fitted and self.model is not None:
                # Use the trained neural network model
                prediction_result = self._neural_network_prediction(features, symbol, timeframe_days)
                model_type = "neural_network_ensemble"
                model_certainty = "High"
            else:
                # Use enhanced technical analysis until model is trained
                prediction_result = self._enhanced_technical_analysis(features, symbol, timeframe_days)
                model_type = "technical_analysis_enhanced"
                model_certainty = "Medium"

            # Add comprehensive information
            prediction_result.update({
                'current_price': current_price,
                'model_type': model_type,
                'model_certainty': model_certainty,
                'model_version': len(self.training_data),
                'timestamp': datetime.now().isoformat(),
                'training_samples': len(self.training_data),
                'total_predictions': self.call_count
            })

            return prediction_result

        except Exception as e:
            logger.error(f"❌ Unified prediction failed: {e}")
            return self._emergency_fallback(symbol, timeframe_days)

    def _neural_network_prediction(self, features: Dict[str, float], symbol: str, timeframe_days: int) -> Dict[str, Any]:
        """Make prediction using the trained neural network ensemble"""
        try:
            # Prepare feature vector
            feature_vector = self._prepare_feature_vector(features)

            # Scale features
            feature_array = np.array(feature_vector).reshape(1, -1)
            feature_array = self.imputer.transform(feature_array)
            feature_array = self.scaler.transform(feature_array)

            # Get prediction probabilities
            prediction_proba = self.model.predict_proba(feature_array)[0]
            prediction_class = self.model.predict(feature_array)[0]

            # Map to prediction
            class_mapping = {0: 'SELL', 1: 'HOLD', 2: 'BUY'}
            prediction = class_mapping.get(prediction_class, 'HOLD')
            confidence = float(max(prediction_proba) * 100)

            # Calculate target price based on neural network confidence
            current_price = features.get('current_price', 0)

            if prediction == "BUY":
                price_change = (confidence / 100) * 0.25  # Up to 25% gain
                target_price = current_price * (1 + price_change)
            elif prediction == "SELL":
                price_change = (confidence / 100) * 0.25  # Up to 25% loss
                target_price = current_price * (1 - price_change)
            else:
                target_price = current_price

            return {
                'symbol': symbol,
                'prediction': prediction,
                'confidence': round(confidence, 1),
                'target_price': round(target_price, 2),
                'timeframe_days': timeframe_days,
                'prediction_probabilities': {
                    'SELL': round(float(prediction_proba[0]) * 100, 1),
                    'HOLD': round(float(prediction_proba[1]) * 100, 1),
                    'BUY': round(float(prediction_proba[2]) * 100, 1)
                }
            }

        except Exception as e:
            logger.warning(f"⚠️ Neural network prediction failed: {e}")
            return self._enhanced_technical_analysis(features, symbol, timeframe_days)

    def _enhanced_technical_analysis(self, features: Dict[str, float], symbol: str, timeframe_days: int) -> Dict[str, Any]:
        """Enhanced technical analysis with comprehensive indicators"""
        try:
            # Get all technical indicators
            rsi = features.get('rsi', 50)
            price_change_1d = features.get('price_change_1d', 0)
            price_change_5d = features.get('price_change_5d', 0)
            price_change_20d = features.get('price_change_20d', 0)
            price_to_ma5 = features.get('price_to_ma5', 0)
            price_to_ma20 = features.get('price_to_ma20', 0)
            price_to_ma50 = features.get('price_to_ma50', 0)
            price_to_ma200 = features.get('price_to_ma200', 0)
            bb_position = features.get('bb_position', 0.5)
            volume_ratio = features.get('volume_ratio', 1.0)
            volatility = features.get('volatility', 0.2)

            # Advanced scoring system
            score = 0
            confidence_factors = []

            # RSI analysis
            if rsi < 25:  # Extremely oversold
                score += 3
                confidence_factors.append("extremely_oversold")
            elif rsi < 35:  # Oversold
                score += 2
                confidence_factors.append("oversold")
            elif rsi > 75:  # Extremely overbought
                score -= 3
                confidence_factors.append("extremely_overbought")
            elif rsi > 65:  # Overbought
                score -= 2
                confidence_factors.append("overbought")

            # Price momentum analysis
            if price_change_1d > 3:
                score += 1
            elif price_change_1d < -3:
                score -= 1

            if price_change_5d > 8:
                score += 2
            elif price_change_5d < -8:
                score -= 2

            if price_change_20d > 15:
                score += 1
            elif price_change_20d < -15:
                score -= 1

            # Moving average trend analysis
            if price_to_ma5 > 3 and price_to_ma20 > 3 and price_to_ma50 > 3:
                score += 2
                confidence_factors.append("strong_uptrend")
            elif price_to_ma5 < -3 and price_to_ma20 < -3 and price_to_ma50 < -3:
                score -= 2
                confidence_factors.append("strong_downtrend")

            # Bollinger Bands position
            if bb_position < 0.1:  # Near lower band
                score += 1
                confidence_factors.append("near_support")
            elif bb_position > 0.9:  # Near upper band
                score -= 1
                confidence_factors.append("near_resistance")

            # Volume confirmation
            if volume_ratio > 2.0:
                confidence_factors.append("very_high_volume")
            elif volume_ratio > 1.5:
                confidence_factors.append("high_volume")

            # Determine prediction and confidence
            if score >= 4:
                prediction = "BUY"
                base_confidence = 75
            elif score >= 2:
                prediction = "BUY"
                base_confidence = 65
            elif score <= -4:
                prediction = "SELL"
                base_confidence = 75
            elif score <= -2:
                prediction = "SELL"
                base_confidence = 65
            else:
                prediction = "HOLD"
                base_confidence = 55

            # Adjust confidence based on factors and timeframe
            confidence = base_confidence + len(confidence_factors) * 3

            if timeframe_days <= 7:
                confidence = min(confidence * 1.1, 95)
            elif timeframe_days >= 60:
                confidence = confidence * 0.85

            # Calculate target price
            current_price = features.get('current_price', 0)

            if prediction == "BUY":
                price_change = (confidence / 100) * 0.20
                target_price = current_price * (1 + price_change)
            elif prediction == "SELL":
                price_change = (confidence / 100) * 0.20
                target_price = current_price * (1 - price_change)
            else:
                target_price = current_price

            return {
                'symbol': symbol,
                'prediction': prediction,
                'confidence': round(confidence, 1),
                'target_price': round(target_price, 2),
                'timeframe_days': timeframe_days,
                'analysis_factors': confidence_factors,
                'technical_score': score
            }

        except Exception as e:
            logger.warning(f"⚠️ Enhanced technical analysis failed: {e}")
            return self._emergency_fallback(symbol, timeframe_days)

    def _prepare_feature_vector(self, features: Dict[str, float]) -> List[float]:
        """Prepare standardized feature vector for the model"""
        return [
            features.get('price_change_1d', 0),
            features.get('price_change_5d', 0),
            features.get('price_change_20d', 0),
            features.get('price_change_60d', 0),
            features.get('rsi', 50),
            features.get('bb_position', 0.5),
            features.get('volatility', 0.2),
            features.get('volume_ratio', 1.0),
            features.get('price_to_ma5', 0),
            features.get('price_to_ma20', 0),
            features.get('price_to_ma50', 0),
            features.get('price_to_ma200', 0),
            features.get('market_cap_log', 20),
            features.get('pe_ratio', 20)
        ]

    def _store_and_retrain(self, symbol: str, features: Dict[str, float], prediction_result: Dict[str, Any]):
        """Store prediction data and retrain model with every call"""
        try:
            # Store training data
            training_record = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'features': features,
                'prediction': prediction_result['prediction'],
                'confidence': prediction_result['confidence'],
                'current_price': prediction_result['current_price'],
                'target_price': prediction_result['target_price'],
                'timeframe_days': prediction_result['timeframe_days']
            }

            self.training_data.append(training_record)

            # Keep reasonable amount of training data
            if len(self.training_data) > 2000:
                self.training_data = self.training_data[-2000:]

            # Save training data every 5 calls
            if self.call_count % 5 == 0:
                self._save_training_data()

            # Retrain model with every call once we have enough data
            if len(self.training_data) >= 10:
                self._train_unified_model()

        except Exception as e:
            logger.warning(f"⚠️ Could not store and retrain: {e}")

    def _train_unified_model(self):
        """Train the unified model with all available data"""
        try:
            if len(self.training_data) < 10:
                return

            logger.info(f"🎓 Training unified model with {len(self.training_data)} samples...")

            # Prepare training data
            X = []
            y = []

            for record in self.training_data:
                features = record['features']
                feature_vector = self._prepare_feature_vector(features)

                # Create label based on prediction
                prediction = record['prediction']
                if prediction == 'BUY':
                    label = 2
                elif prediction == 'SELL':
                    label = 0
                else:
                    label = 1

                X.append(feature_vector)
                y.append(label)

            # Convert to arrays
            X = np.array(X)
            y = np.array(y)

            # Fit preprocessors
            X_processed = self.imputer.fit_transform(X)
            X_processed = self.scaler.fit_transform(X_processed)

            # Train the unified ensemble model
            self.model.fit(X_processed, y)
            self.is_fitted = True

            # Save the model
            self._save_unified_model()

            logger.info(f"✅ Unified model trained successfully with {len(X)} samples")

        except Exception as e:
            logger.warning(f"⚠️ Model training failed: {e}")

    def _train_model_with_existing_data(self):
        """Train model with existing data on startup"""
        try:
            if len(self.training_data) >= 10:
                self._train_unified_model()
        except Exception as e:
            logger.warning(f"⚠️ Could not train with existing data: {e}")

    def _load_training_data(self):
        """Load existing training data"""
        try:
            training_file = os.path.join(self.models_dir, "unified_training_data.json")
            if os.path.exists(training_file):
                with open(training_file, 'r') as f:
                    self.training_data = json.load(f)
                logger.info(f"📊 Loaded {len(self.training_data)} training records")

                # Try to load existing model
                self._load_unified_model()
        except Exception as e:
            logger.warning(f"⚠️ Could not load training data: {e}")
            self.training_data = []

    def _save_training_data(self):
        """Save training data to disk"""
        try:
            training_file = os.path.join(self.models_dir, "unified_training_data.json")
            with open(training_file, 'w') as f:
                json.dump(self.training_data, f)
        except Exception as e:
            logger.warning(f"⚠️ Could not save training data: {e}")

    def _load_unified_model(self):
        """Load the unified model if available"""
        try:
            model_file = os.path.join(self.models_dir, "unified_model.joblib")
            scaler_file = os.path.join(self.models_dir, "unified_scaler.joblib")
            imputer_file = os.path.join(self.models_dir, "unified_imputer.joblib")

            if all(os.path.exists(f) for f in [model_file, scaler_file, imputer_file]):
                self.model = joblib.load(model_file)
                self.scaler = joblib.load(scaler_file)
                self.imputer = joblib.load(imputer_file)
                self.is_fitted = True
                logger.info("✅ Loaded existing unified model")
            else:
                logger.info("📚 No existing unified model found")
        except Exception as e:
            logger.warning(f"⚠️ Could not load unified model: {e}")

    def _save_unified_model(self):
        """Save the unified model"""
        try:
            if self.model is not None and self.is_fitted:
                model_file = os.path.join(self.models_dir, "unified_model.joblib")
                scaler_file = os.path.join(self.models_dir, "unified_scaler.joblib")
                imputer_file = os.path.join(self.models_dir, "unified_imputer.joblib")

                joblib.dump(self.model, model_file)
                joblib.dump(self.scaler, scaler_file)
                joblib.dump(self.imputer, imputer_file)

                logger.info("💾 Unified model saved successfully")
        except Exception as e:
            logger.warning(f"⚠️ Could not save unified model: {e}")

    def _emergency_fallback(self, symbol: str, timeframe_days: int) -> Dict[str, Any]:
        """Emergency fallback when everything fails"""
        try:
            # Try to get at least current price
            ticker = yf.Ticker(symbol)
            info = ticker.info
            current_price = info.get('currentPrice', info.get('regularMarketPrice', 100.0))

            return {
                'symbol': symbol,
                'prediction': 'HOLD',
                'confidence': 50.0,
                'current_price': float(current_price),
                'target_price': float(current_price),
                'timeframe_days': timeframe_days,
                'model_type': 'emergency_fallback',
                'model_certainty': 'Low',
                'model_version': 0,
                'timestamp': datetime.now().isoformat(),
                'training_samples': len(self.training_data),
                'total_predictions': self.call_count
            }
        except:
            return {
                'symbol': symbol,
                'prediction': 'HOLD',
                'confidence': 50.0,
                'current_price': 100.0,
                'target_price': 100.0,
                'timeframe_days': timeframe_days,
                'model_type': 'emergency_fallback',
                'model_certainty': 'Low',
                'model_version': 0,
                'timestamp': datetime.now().isoformat(),
                'training_samples': len(self.training_data),
                'total_predictions': self.call_count
            }

    def get_model_status(self) -> Dict[str, Any]:
        """Get comprehensive model status"""
        return {
            'model_available': self.is_fitted,
            'model_type': 'unified_neural_network_ensemble',
            'training_samples': len(self.training_data),
            'total_predictions': self.call_count,
            'is_fitted': self.is_fitted,
            'last_retrain': 'automatic_with_every_call' if self.is_fitted else 'not_trained_yet'
        }

    # Backward compatibility methods
    def predict(self, symbol: str, timeframe_days: int = 30) -> Dict[str, Any]:
        """Backward compatibility wrapper"""
        return self.predict_stock_movement(symbol, timeframe_days)

    def _make_ml_prediction(self, features: Dict[str, float], symbol: str, timeframe_days: int) -> Dict[str, Any]:
        """Make prediction using trained ML model"""
        try:
            # Prepare features for model
            feature_values = [
                features.get('price_change_1d', 0),
                features.get('price_change_5d', 0),
                features.get('price_change_20d', 0),
                features.get('rsi', 50),
                features.get('volatility', 0.2),
                features.get('volume_ratio', 1.0),
                features.get('price_to_ma5', 0),
                features.get('price_to_ma20', 0)
            ]

            # Scale features
            feature_array = np.array(feature_values).reshape(1, -1)
            feature_array = self.imputer.transform(feature_array)
            feature_array = self.scaler.transform(feature_array)

            # Make prediction
            prediction_proba = self.model.predict_proba(feature_array)[0]
            prediction_class = self.model.predict(feature_array)[0]

            # Map prediction
            class_mapping = {0: 'SELL', 1: 'HOLD', 2: 'BUY'}
            prediction = class_mapping.get(prediction_class, 'HOLD')
            confidence = float(max(prediction_proba) * 100)

            current_price = features.get('current_price', 0)

            # Calculate target price based on prediction and confidence
            if prediction == "BUY":
                target_multiplier = 1 + (confidence / 100 * 0.20)  # Up to 20% gain
            elif prediction == "SELL":
                target_multiplier = 1 - (confidence / 100 * 0.20)  # Up to 20% loss
            else:
                target_multiplier = 1.0

            target_price = current_price * target_multiplier

            return {
                'symbol': symbol,
                'prediction': prediction,
                'confidence': round(confidence, 1),
                'current_price': current_price,
                'target_price': round(target_price, 2),
                'timeframe_days': timeframe_days,
                'model_type': 'machine_learning',
                'model_version': len(self.training_data),
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.warning(f"⚠️ ML prediction failed: {e}")
            return self._smart_technical_prediction(features, symbol, timeframe_days)

    def _smart_fallback(self, symbol: str, timeframe_days: int) -> Dict[str, Any]:
        """Smart fallback when everything else fails"""
        try:
            # Try to get at least current price
            ticker = yf.Ticker(symbol)
            info = ticker.info
            current_price = info.get('currentPrice', info.get('regularMarketPrice', 100.0))

            return {
                'symbol': symbol,
                'prediction': 'HOLD',
                'confidence': 50.0,
                'current_price': float(current_price),
                'target_price': float(current_price),
                'timeframe_days': timeframe_days,
                'model_type': 'fallback',
                'model_version': 0,
                'timestamp': datetime.now().isoformat()
            }
        except:
            return {
                'symbol': symbol,
                'prediction': 'HOLD',
                'confidence': 50.0,
                'current_price': 100.0,
                'target_price': 100.0,
                'timeframe_days': timeframe_days,
                'model_type': 'emergency_fallback',
                'model_version': 0,
                'timestamp': datetime.now().isoformat()
            }

    def _store_prediction_for_learning(self, symbol: str, features: Dict[str, float], prediction_result: Dict[str, Any]):
        """Store prediction for future learning"""
        try:
            learning_record = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'features': features,
                'prediction': prediction_result['prediction'],
                'confidence': prediction_result['confidence'],
                'current_price': prediction_result['current_price'],
                'target_price': prediction_result['target_price'],
                'timeframe_days': prediction_result['timeframe_days']
            }

            self.prediction_history.append(learning_record)

            # Keep only last 1000 predictions to avoid memory issues
            if len(self.prediction_history) > 1000:
                self.prediction_history = self.prediction_history[-1000:]

            # Save to disk periodically
            if len(self.prediction_history) % 10 == 0:
                self._save_learning_data()

        except Exception as e:
            logger.warning(f"⚠️ Could not store prediction for learning: {e}")

    def _retrain_model(self):
        """Retrain the model with accumulated data"""
        try:
            logger.info("🎓 Retraining model with new data...")

            # Prepare training data from prediction history
            if len(self.prediction_history) < 20:
                return

            # Create training samples
            X = []
            y = []

            for record in self.prediction_history[-200:]:  # Use last 200 predictions
                features = record['features']
                feature_vector = [
                    features.get('price_change_1d', 0),
                    features.get('price_change_5d', 0),
                    features.get('price_change_20d', 0),
                    features.get('rsi', 50),
                    features.get('volatility', 0.2),
                    features.get('volume_ratio', 1.0),
                    features.get('price_to_ma5', 0),
                    features.get('price_to_ma20', 0)
                ]

                # Create label based on prediction (simplified for now)
                prediction = record['prediction']
                if prediction == 'BUY':
                    label = 2
                elif prediction == 'SELL':
                    label = 0
                else:
                    label = 1

                X.append(feature_vector)
                y.append(label)

            if len(X) < 10:
                return

            # Prepare data
            X = np.array(X)
            y = np.array(y)

            # Fit preprocessors
            X_processed = self.imputer.fit_transform(X)
            X_processed = self.scaler.fit_transform(X_processed)

            # Train model
            self.model = RandomForestClassifier(
                n_estimators=50,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
            self.model.fit(X_processed, y)

            # Save model
            self._save_model()

            logger.info(f"✅ Model retrained with {len(X)} samples")

        except Exception as e:
            logger.warning(f"⚠️ Model retraining failed: {e}")

    def _load_learning_data(self):
        """Load existing learning data"""
        try:
            history_file = os.path.join(self.models_dir, "prediction_history.json")
            if os.path.exists(history_file):
                with open(history_file, 'r') as f:
                    self.prediction_history = json.load(f)
                logger.info(f"📊 Loaded {len(self.prediction_history)} prediction records")
        except Exception as e:
            logger.warning(f"⚠️ Could not load learning data: {e}")
            self.prediction_history = []

    def _save_learning_data(self):
        """Save learning data to disk"""
        try:
            history_file = os.path.join(self.models_dir, "prediction_history.json")
            with open(history_file, 'w') as f:
                json.dump(self.prediction_history, f)
        except Exception as e:
            logger.warning(f"⚠️ Could not save learning data: {e}")

    def _load_model(self):
        """Load existing model if available"""
        try:
            model_file = os.path.join(self.models_dir, "self_learning_model.joblib")
            scaler_file = os.path.join(self.models_dir, "scaler.joblib")
            imputer_file = os.path.join(self.models_dir, "imputer.joblib")

            if all(os.path.exists(f) for f in [model_file, scaler_file, imputer_file]):
                self.model = joblib.load(model_file)
                self.scaler = joblib.load(scaler_file)
                self.imputer = joblib.load(imputer_file)
                logger.info("✅ Loaded existing trained model")
            else:
                logger.info("📚 No existing model found - starting fresh")
        except Exception as e:
            logger.warning(f"⚠️ Could not load existing model: {e}")

    def _save_model(self):
        """Save current model to disk"""
        try:
            if self.model is not None:
                model_file = os.path.join(self.models_dir, "self_learning_model.joblib")
                scaler_file = os.path.join(self.models_dir, "scaler.joblib")
                imputer_file = os.path.join(self.models_dir, "imputer.joblib")

                joblib.dump(self.model, model_file)
                joblib.dump(self.scaler, scaler_file)
                joblib.dump(self.imputer, imputer_file)

                logger.info("💾 Model saved successfully")
        except Exception as e:
            logger.warning(f"⚠️ Could not save model: {e}")

    def get_model_status(self) -> Dict[str, Any]:
        """Get current model status"""
        return {
            'model_available': self.model is not None,
            'training_samples': len(self.prediction_history),
            'total_predictions': self.call_count,
            'model_type': 'self_learning_random_forest' if self.model else 'technical_analysis',
            'last_retrain': 'automatic' if self.model else 'not_trained'
        }

    # Backward compatibility methods
    def predict(self, symbol: str, timeframe_days: int = 30) -> Dict[str, Any]:
        """Backward compatibility wrapper"""
        return self.predict_stock_movement(symbol, timeframe_days)
