"""
StockTrek Neural Network Module
Professional wrapper for the autonomous neural network system
"""

import logging
from typing import Dict, Any
from .autonomous_neural_network import AutonomousNeuralNetwork

logger = logging.getLogger(__name__)


class NeuralNetworkPredictor:
    """Professional neural network predictor with autonomous capabilities"""
    
    def __init__(self):
        """Initialize the autonomous neural network system"""
        self.autonomous_nn = AutonomousNeuralNetwork()
        logger.info("🚀 Autonomous Neural Network System initialized")
    
    def predict(self, symbol: str, timeframe_days: int = 30) -> Dict[str, Any]:
        """Make a prediction using the autonomous neural network"""
        try:
            result = self.autonomous_nn.predict(symbol, timeframe_days)
            
            # Convert to expected format for backward compatibility
            return {
                'symbol': result['symbol'],
                'prediction': result['prediction'],
                'confidence': result['confidence'],
                'timeframe_days': result['timeframe_days'],
                'model_certainty': 'High' if result['confidence'] > 70 else 'Medium' if result['confidence'] > 50 else 'Low',
                'timestamp': result['timestamp'],
                'model_type': result['model_type'],
                'model_version': result['model_version']
            }
            
        except Exception as e:
            logger.error(f"❌ Neural network prediction failed for {symbol}: {e}")
            return self._emergency_fallback(symbol, timeframe_days)
    
    def _emergency_fallback(self, symbol: str, timeframe_days: int) -> Dict[str, Any]:
        """Emergency fallback when everything fails"""
        return {
            'symbol': symbol,
            'prediction': 'HOLD',
            'confidence': 50.0,
            'timeframe_days': timeframe_days,
            'model_certainty': 'Unknown',
            'timestamp': '2025-07-01T00:00:00',
            'model_type': 'emergency_fallback',
            'model_version': 0
        }
    
    def get_model_status(self) -> Dict[str, Any]:
        """Get comprehensive model status"""
        return self.autonomous_nn.get_model_status()
    
    def train_model(self, num_stocks: int = 100) -> bool:
        """Trigger autonomous training"""
        return self.autonomous_nn.autonomous_training(num_stocks)
    
    def discover_stocks(self) -> int:
        """Discover all available stocks"""
        stocks = self.autonomous_nn.stock_discovery.discover_all_stocks()
        return len(stocks)
