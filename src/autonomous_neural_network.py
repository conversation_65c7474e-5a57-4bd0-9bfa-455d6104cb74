"""
StockTrek Autonomous Neural Network System
Professional-grade neural network with autonomous stock discovery and self-learning
"""

import os
import sys
import numpy as np
import pandas as pd
import yfinance as yf
import joblib
import logging
import json
import requests
import time
import warnings
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Set
from concurrent.futures import ThreadPoolExecutor, as_completed

# Machine Learning imports
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder, RobustScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, classification_report, precision_recall_fscore_support
from sklearn.impute import SimpleImputer

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)


class AutonomousStockDiscovery:
    """Discovers all publicly traded stocks autonomously"""
    
    def __init__(self):
        self.discovered_stocks: Set[str] = set()
        self.stock_cache_file = Path("data/discovered_stocks.json")
        self.stock_cache_file.parent.mkdir(exist_ok=True)
        self._load_stock_cache()
    
    def _load_stock_cache(self):
        """Load previously discovered stocks from cache"""
        try:
            if self.stock_cache_file.exists():
                with open(self.stock_cache_file, 'r') as f:
                    data = json.load(f)
                    self.discovered_stocks = set(data.get('stocks', []))
                    logger.info(f"📊 Loaded {len(self.discovered_stocks)} cached stocks")
        except Exception as e:
            logger.warning(f"⚠️ Could not load stock cache: {e}")
    
    def _save_stock_cache(self):
        """Save discovered stocks to cache"""
        try:
            data = {
                'stocks': list(self.discovered_stocks),
                'last_updated': datetime.now().isoformat(),
                'total_count': len(self.discovered_stocks)
            }
            with open(self.stock_cache_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"❌ Could not save stock cache: {e}")
    
    def discover_all_stocks(self, max_workers: int = 10) -> Set[str]:
        """Autonomously discover all publicly traded stocks"""
        try:
            logger.info("🔍 Starting autonomous stock discovery...")
            
            # Get stocks from major exchanges
            exchanges = ['NASDAQ', 'NYSE', 'AMEX']
            new_stocks = set()
            
            for exchange in exchanges:
                try:
                    stocks = self._get_stocks_from_exchange(exchange)
                    new_stocks.update(stocks)
                    logger.info(f"📈 Found {len(stocks)} stocks from {exchange}")
                except Exception as e:
                    logger.warning(f"⚠️ Could not get stocks from {exchange}: {e}")
            
            # Add popular indices and ETFs
            popular_stocks = self._get_popular_stocks()
            new_stocks.update(popular_stocks)
            
            # Filter valid stocks
            valid_stocks = self._validate_stocks(new_stocks, max_workers)
            
            self.discovered_stocks.update(valid_stocks)
            self._save_stock_cache()
            
            logger.info(f"✅ Discovery complete: {len(self.discovered_stocks)} total stocks")
            return self.discovered_stocks
            
        except Exception as e:
            logger.error(f"❌ Stock discovery failed: {e}")
            return self.discovered_stocks
    
    def _get_stocks_from_exchange(self, exchange: str) -> Set[str]:
        """Get stock symbols from a specific exchange"""
        try:
            # This would typically use a financial data API
            # For now, we'll use a comprehensive list approach
            if exchange == 'NASDAQ':
                return self._get_nasdaq_stocks()
            elif exchange == 'NYSE':
                return self._get_nyse_stocks()
            elif exchange == 'AMEX':
                return self._get_amex_stocks()
            return set()
        except Exception as e:
            logger.warning(f"⚠️ Error getting {exchange} stocks: {e}")
            return set()
    
    def _get_nasdaq_stocks(self) -> Set[str]:
        """Get NASDAQ stock symbols"""
        # Major NASDAQ stocks - in production this would use an API
        nasdaq_stocks = {
            'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'TSLA', 'META', 'NVDA',
            'NFLX', 'ADBE', 'CRM', 'PYPL', 'INTC', 'AMD', 'QCOM', 'AVGO',
            'TXN', 'COST', 'TMUS', 'CSCO', 'PEP', 'CMCSA', 'HON', 'SBUX',
            'INTU', 'AMGN', 'ISRG', 'BKNG', 'GILD', 'MDLZ', 'ADP', 'VRTX',
            'FISV', 'REGN', 'ATVI', 'CSX', 'ILMN', 'MELI', 'LRCX', 'ADI',
            'KLAC', 'SNPS', 'CDNS', 'MRVL', 'ORLY', 'WDAY', 'NXPI', 'ASML'
        }
        return nasdaq_stocks
    
    def _get_nyse_stocks(self) -> Set[str]:
        """Get NYSE stock symbols"""
        # Major NYSE stocks - in production this would use an API
        nyse_stocks = {
            'JPM', 'JNJ', 'V', 'PG', 'UNH', 'HD', 'MA', 'DIS', 'BAC', 'XOM',
            'ABBV', 'PFE', 'KO', 'TMO', 'ACN', 'VZ', 'ADBE', 'NKE', 'MRK',
            'WMT', 'CRM', 'DHR', 'LLY', 'ORCL', 'ABT', 'TXN', 'COST', 'NEE',
            'CVX', 'PM', 'MDT', 'BMY', 'AMGN', 'UNP', 'LOW', 'QCOM', 'HON',
            'UPS', 'IBM', 'SBUX', 'T', 'CAT', 'GS', 'SPGI', 'BLK', 'AXP',
            'DE', 'MMM', 'BA', 'GE', 'WFC', 'MS', 'C', 'F', 'GM', 'DAL'
        }
        return nyse_stocks
    
    def _get_amex_stocks(self) -> Set[str]:
        """Get AMEX stock symbols"""
        # Major AMEX stocks and ETFs
        amex_stocks = {
            'SPY', 'QQQ', 'IWM', 'EFA', 'VTI', 'VEA', 'VWO', 'AGG', 'LQD',
            'HYG', 'TLT', 'GLD', 'SLV', 'USO', 'XLE', 'XLF', 'XLK', 'XLV',
            'XLI', 'XLP', 'XLY', 'XLU', 'XLB', 'XLRE', 'XLC', 'IYR', 'EWJ'
        }
        return amex_stocks
    
    def _get_popular_stocks(self) -> Set[str]:
        """Get additional popular stocks and crypto-related stocks"""
        popular = {
            'BRK.A', 'BRK.B', 'COIN', 'SQ', 'ROKU', 'ZOOM', 'SHOP', 'TWLO',
            'OKTA', 'SNOW', 'PLTR', 'RBLX', 'HOOD', 'SOFI', 'UPST', 'AFRM',
            'DKNG', 'PENN', 'FUBO', 'WISH', 'CLOV', 'AMC', 'GME', 'BB',
            'NOK', 'SNDL', 'TLRY', 'CGC', 'ACB', 'HEXO', 'OGI', 'CRON'
        }
        return popular
    
    def _validate_stocks(self, stocks: Set[str], max_workers: int = 10) -> Set[str]:
        """Validate that stocks have available data"""
        valid_stocks = set()
        
        def validate_single_stock(symbol: str) -> Optional[str]:
            try:
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period="5d")
                if len(hist) >= 3:  # At least 3 days of data
                    return symbol
            except:
                pass
            return None
        
        logger.info(f"🔍 Validating {len(stocks)} stocks...")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_stock = {executor.submit(validate_single_stock, stock): stock for stock in stocks}
            
            for future in as_completed(future_to_stock):
                result = future.result()
                if result:
                    valid_stocks.add(result)
        
        logger.info(f"✅ Validated {len(valid_stocks)} stocks with available data")
        return valid_stocks
    
    def get_random_stocks(self, count: int = 50) -> List[str]:
        """Get a random sample of discovered stocks for training"""
        if not self.discovered_stocks:
            self.discover_all_stocks()
        
        available_stocks = list(self.discovered_stocks)
        if len(available_stocks) <= count:
            return available_stocks
        
        return np.random.choice(available_stocks, size=count, replace=False).tolist()


class RobustFeatureExtractor:
    """Extracts features with graceful handling of missing data"""
    
    def __init__(self):
        self.imputer = SimpleImputer(strategy='median')
        self.feature_names = []
    
    def extract_features(self, symbol: str, timeframe_days: int = 30) -> Optional[Dict[str, float]]:
        """Extract features with robust error handling"""
        try:
            # Get stock data with error handling
            ticker = yf.Ticker(symbol)
            
            # Try different periods if primary fails
            periods = ["1y", "6mo", "3mo", "1mo"]
            hist_data = None
            
            for period in periods:
                try:
                    hist_data = ticker.history(period=period)
                    if len(hist_data) >= 30:  # Need minimum data
                        break
                except:
                    continue
            
            if hist_data is None or len(hist_data) < 10:
                logger.warning(f"⚠️ Insufficient data for {symbol}")
                return None
            
            # Extract features with error handling
            features = {}
            
            # Basic price features
            try:
                current_price = hist_data['Close'].iloc[-1]
                features['current_price'] = float(current_price)
                features['price_change_1d'] = self._safe_pct_change(hist_data['Close'], 1)
                features['price_change_5d'] = self._safe_pct_change(hist_data['Close'], 5)
                features['price_change_20d'] = self._safe_pct_change(hist_data['Close'], 20)
            except Exception as e:
                logger.warning(f"⚠️ Price features failed for {symbol}: {e}")
                features.update({'current_price': 0, 'price_change_1d': 0, 'price_change_5d': 0, 'price_change_20d': 0})
            
            # Technical indicators with error handling
            try:
                features.update(self._extract_technical_indicators(hist_data))
            except Exception as e:
                logger.warning(f"⚠️ Technical indicators failed for {symbol}: {e}")
                features.update(self._get_default_technical_features())
            
            # Volume features
            try:
                features.update(self._extract_volume_features(hist_data))
            except Exception as e:
                logger.warning(f"⚠️ Volume features failed for {symbol}: {e}")
                features.update(self._get_default_volume_features())
            
            # Timeframe features
            features['timeframe_days'] = timeframe_days
            features['timeframe_normalized'] = min(timeframe_days / 365.0, 1.0)
            
            # Replace any NaN or infinite values
            features = self._clean_features(features)
            
            return features
            
        except Exception as e:
            logger.error(f"❌ Feature extraction failed for {symbol}: {e}")
            return None

    def _safe_pct_change(self, series: pd.Series, periods: int) -> float:
        """Calculate percentage change with error handling"""
        try:
            if len(series) <= periods:
                return 0.0
            current = series.iloc[-1]
            previous = series.iloc[-periods-1]
            if previous == 0:
                return 0.0
            return float((current - previous) / previous * 100)
        except:
            return 0.0

    def _extract_technical_indicators(self, data: pd.DataFrame) -> Dict[str, float]:
        """Extract technical indicators with error handling"""
        indicators = {}

        try:
            # RSI
            delta = data['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            indicators['rsi'] = float(rsi.iloc[-1]) if not pd.isna(rsi.iloc[-1]) else 50.0
        except:
            indicators['rsi'] = 50.0

        try:
            # Moving averages
            indicators['sma_10'] = float(data['Close'].rolling(10).mean().iloc[-1])
            indicators['sma_20'] = float(data['Close'].rolling(20).mean().iloc[-1])
            indicators['sma_50'] = float(data['Close'].rolling(50).mean().iloc[-1])
        except:
            current_price = data['Close'].iloc[-1]
            indicators.update({'sma_10': current_price, 'sma_20': current_price, 'sma_50': current_price})

        try:
            # MACD
            ema12 = data['Close'].ewm(span=12).mean()
            ema26 = data['Close'].ewm(span=26).mean()
            macd = ema12 - ema26
            signal = macd.ewm(span=9).mean()
            indicators['macd'] = float(macd.iloc[-1])
            indicators['macd_signal'] = float(signal.iloc[-1])
            indicators['macd_histogram'] = float((macd - signal).iloc[-1])
        except:
            indicators.update({'macd': 0.0, 'macd_signal': 0.0, 'macd_histogram': 0.0})

        try:
            # Bollinger Bands
            sma20 = data['Close'].rolling(20).mean()
            std20 = data['Close'].rolling(20).std()
            bb_upper = sma20 + 2 * std20
            bb_lower = sma20 - 2 * std20
            current_price = data['Close'].iloc[-1]
            indicators['bb_upper'] = float(bb_upper.iloc[-1])
            indicators['bb_lower'] = float(bb_lower.iloc[-1])
            bb_range = indicators['bb_upper'] - indicators['bb_lower']
            indicators['bb_position'] = float((current_price - indicators['bb_lower']) / bb_range) if bb_range > 0 else 0.5
        except:
            current_price = data['Close'].iloc[-1]
            indicators.update({'bb_upper': current_price * 1.1, 'bb_lower': current_price * 0.9, 'bb_position': 0.5})

        try:
            # Volatility
            returns = data['Close'].pct_change()
            volatility = returns.rolling(20).std() * np.sqrt(252)
            indicators['volatility'] = float(volatility.iloc[-1])
        except:
            indicators['volatility'] = 0.02

        return indicators

    def _extract_volume_features(self, data: pd.DataFrame) -> Dict[str, float]:
        """Extract volume features with error handling"""
        volume_features = {}

        try:
            current_volume = data['Volume'].iloc[-1]
            avg_volume = data['Volume'].rolling(20).mean().iloc[-1]
            volume_features['volume'] = float(current_volume)
            volume_features['volume_avg_20'] = float(avg_volume)
            volume_features['volume_ratio'] = float(current_volume / avg_volume) if avg_volume > 0 else 1.0
        except:
            volume_features.update({'volume': 1000000, 'volume_avg_20': 1000000, 'volume_ratio': 1.0})

        return volume_features

    def _get_default_technical_features(self) -> Dict[str, float]:
        """Return default technical features when calculation fails"""
        return {
            'rsi': 50.0, 'sma_10': 100.0, 'sma_20': 100.0, 'sma_50': 100.0,
            'macd': 0.0, 'macd_signal': 0.0, 'macd_histogram': 0.0,
            'bb_upper': 110.0, 'bb_lower': 90.0, 'bb_position': 0.5,
            'volatility': 0.02
        }

    def _get_default_volume_features(self) -> Dict[str, float]:
        """Return default volume features when calculation fails"""
        return {'volume': 1000000, 'volume_avg_20': 1000000, 'volume_ratio': 1.0}

    def _clean_features(self, features: Dict[str, float]) -> Dict[str, float]:
        """Clean features by replacing NaN and infinite values"""
        cleaned = {}
        for key, value in features.items():
            if pd.isna(value) or np.isinf(value):
                # Use reasonable defaults based on feature type
                if 'price' in key.lower():
                    cleaned[key] = 100.0
                elif 'volume' in key.lower():
                    cleaned[key] = 1000000.0
                elif 'ratio' in key.lower() or 'position' in key.lower():
                    cleaned[key] = 1.0
                elif 'rsi' in key.lower():
                    cleaned[key] = 50.0
                else:
                    cleaned[key] = 0.0
            else:
                cleaned[key] = float(value)
        return cleaned


class AutonomousNeuralNetwork:
    """Professional autonomous neural network with self-learning capabilities"""

    def __init__(self):
        self.models_dir = Path("models")
        self.models_dir.mkdir(exist_ok=True)
        self.data_dir = Path("data")
        self.data_dir.mkdir(exist_ok=True)

        # Core components
        self.stock_discovery = AutonomousStockDiscovery()
        self.feature_extractor = RobustFeatureExtractor()

        # ML components
        self.model = None
        self.scaler = None
        self.label_encoder = None
        self.feature_names = []

        # Performance tracking
        self.performance_tracker = self._load_performance_tracker()

        # Load existing model if available
        self._load_model()

    def _load_performance_tracker(self) -> Dict:
        """Load performance tracking data"""
        tracker_file = self.data_dir / "performance_tracker.json"
        try:
            if tracker_file.exists():
                with open(tracker_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"⚠️ Could not load performance tracker: {e}")

        return {
            'predictions': [],
            'accuracy_history': [],
            'last_training': None,
            'total_predictions': 0,
            'correct_predictions': 0,
            'model_version': 1
        }

    def _save_performance_tracker(self):
        """Save performance tracking data"""
        tracker_file = self.data_dir / "performance_tracker.json"
        try:
            with open(tracker_file, 'w') as f:
                json.dump(self.performance_tracker, f, indent=2)
        except Exception as e:
            logger.error(f"❌ Could not save performance tracker: {e}")

    def _load_model(self) -> bool:
        """Load trained model if available"""
        try:
            model_files = {
                'model': self.models_dir / 'autonomous_model.joblib',
                'scaler': self.models_dir / 'autonomous_scaler.joblib',
                'encoder': self.models_dir / 'autonomous_encoder.joblib',
                'features': self.models_dir / 'autonomous_features.joblib'
            }

            if all(path.exists() for path in model_files.values()):
                self.model = joblib.load(model_files['model'])
                self.scaler = joblib.load(model_files['scaler'])
                self.label_encoder = joblib.load(model_files['encoder'])
                self.feature_names = joblib.load(model_files['features'])

                logger.info("✅ Autonomous neural network model loaded successfully")
                return True
            else:
                logger.info("📚 No existing model found, will train new model")
                return False

        except Exception as e:
            logger.error(f"❌ Error loading model: {e}")
            return False

    def _save_model(self):
        """Save trained model"""
        try:
            model_files = {
                'model': self.models_dir / 'autonomous_model.joblib',
                'scaler': self.models_dir / 'autonomous_scaler.joblib',
                'encoder': self.models_dir / 'autonomous_encoder.joblib',
                'features': self.models_dir / 'autonomous_features.joblib'
            }

            joblib.dump(self.model, model_files['model'])
            joblib.dump(self.scaler, model_files['scaler'])
            joblib.dump(self.label_encoder, model_files['encoder'])
            joblib.dump(self.feature_names, model_files['features'])

            logger.info("💾 Model saved successfully")

        except Exception as e:
            logger.error(f"❌ Error saving model: {e}")

    def autonomous_training(self, num_stocks: int = 100, retrain_threshold: float = 0.85) -> bool:
        """Autonomously train the neural network on discovered stocks"""
        try:
            logger.info("🎓 Starting autonomous neural network training...")

            # Check if retraining is needed
            current_accuracy = self._get_current_accuracy()
            if current_accuracy >= retrain_threshold and self.model is not None:
                logger.info(f"📊 Current accuracy {current_accuracy:.3f} above threshold {retrain_threshold}, skipping training")
                return True

            # Discover stocks autonomously
            logger.info("🔍 Discovering stocks for training...")
            all_stocks = self.stock_discovery.discover_all_stocks()

            if len(all_stocks) < 10:
                logger.error("❌ Insufficient stocks discovered for training")
                return False

            # Select random subset for training
            training_stocks = self.stock_discovery.get_random_stocks(num_stocks)
            logger.info(f"📊 Training on {len(training_stocks)} stocks")

            # Collect training data
            training_data, training_labels = self._collect_training_data(training_stocks)

            if len(training_data) < 100:
                logger.error("❌ Insufficient training data collected")
                return False

            # Train the model
            success = self._train_model(training_data, training_labels)

            if success:
                # Update performance tracker
                self.performance_tracker['last_training'] = datetime.now().isoformat()
                self.performance_tracker['model_version'] += 1
                self._save_performance_tracker()

                logger.info("✅ Autonomous training completed successfully")
                return True
            else:
                logger.error("❌ Training failed")
                return False

        except Exception as e:
            logger.error(f"❌ Autonomous training failed: {e}")
            return False

    def _collect_training_data(self, stocks: List[str]) -> Tuple[List[List[float]], List[str]]:
        """Collect training data from multiple stocks"""
        training_data = []
        training_labels = []

        logger.info(f"📊 Collecting training data from {len(stocks)} stocks...")

        for i, symbol in enumerate(stocks):
            try:
                if i % 10 == 0:
                    logger.info(f"Processing {symbol} ({i+1}/{len(stocks)})...")

                # Get historical data
                ticker = yf.Ticker(symbol)
                hist_data = ticker.history(period="1y")

                if len(hist_data) < 60:
                    continue

                # Generate training samples with different timeframes
                timeframes = [7, 14, 30, 60, 90]

                for timeframe in timeframes:
                    # Generate samples for this timeframe
                    samples = self._generate_samples_for_stock(symbol, hist_data, timeframe)
                    training_data.extend([s[0] for s in samples])
                    training_labels.extend([s[1] for s in samples])

            except Exception as e:
                logger.warning(f"⚠️ Could not process {symbol}: {e}")
                continue

        logger.info(f"✅ Collected {len(training_data)} training samples")
        return training_data, training_labels

    def _generate_samples_for_stock(self, symbol: str, hist_data: pd.DataFrame, timeframe: int) -> List[Tuple[List[float], str]]:
        """Generate training samples for a single stock"""
        samples = []

        try:
            # Generate samples with sliding window
            for i in range(60, len(hist_data) - timeframe - 5):  # Need lookback and forward data
                try:
                    # Extract features for day i
                    current_data = hist_data.iloc[:i+1]

                    # Create a temporary ticker-like object for feature extraction
                    temp_ticker = type('TempTicker', (), {
                        'history': lambda period: current_data,
                        'info': {}
                    })()

                    # Extract features
                    features = self.feature_extractor.extract_features(symbol, timeframe)

                    if features and len(features) > 10:
                        # Calculate future return
                        current_price = hist_data.iloc[i]['Close']
                        future_price = hist_data.iloc[i + timeframe]['Close']
                        return_pct = (future_price - current_price) / current_price * 100

                        # Create label based on return and timeframe
                        if timeframe <= 30:  # Short-term thresholds
                            if return_pct > 5:
                                label = 'BUY'
                            elif return_pct < -5:
                                label = 'SELL'
                            else:
                                label = 'HOLD'
                        else:  # Long-term thresholds
                            if return_pct > 10:
                                label = 'BUY'
                            elif return_pct < -10:
                                label = 'SELL'
                            else:
                                label = 'HOLD'

                        # Convert features to list
                        feature_values = [features[name] for name in sorted(features.keys())]
                        samples.append((feature_values, label))

                except Exception as e:
                    continue

        except Exception as e:
            logger.warning(f"⚠️ Error generating samples for {symbol}: {e}")

        return samples

    def _train_model(self, training_data: List[List[float]], training_labels: List[str]) -> bool:
        """Train the neural network model"""
        try:
            logger.info("🔧 Training neural network model...")

            # Convert to numpy arrays
            X = np.array(training_data)
            y = np.array(training_labels)

            # Get feature names from a sample
            sample_features = self.feature_extractor.extract_features('AAPL', 30)
            if sample_features:
                self.feature_names = sorted(sample_features.keys())

            logger.info(f"📊 Training with {len(X)} samples and {len(self.feature_names)} features")

            # Handle missing values
            imputer = SimpleImputer(strategy='median')
            X = imputer.fit_transform(X)

            # Scale features
            self.scaler = RobustScaler()
            X_scaled = self.scaler.fit_transform(X)

            # Encode labels
            self.label_encoder = LabelEncoder()
            y_encoded = self.label_encoder.fit_transform(y)

            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
            )

            # Create ensemble model for high accuracy
            rf_model = RandomForestClassifier(
                n_estimators=300,
                max_depth=20,
                min_samples_split=3,
                min_samples_leaf=1,
                random_state=42,
                n_jobs=-1
            )

            gb_model = GradientBoostingClassifier(
                n_estimators=200,
                max_depth=10,
                learning_rate=0.1,
                random_state=42
            )

            mlp_model = MLPClassifier(
                hidden_layer_sizes=(200, 100, 50),
                activation='relu',
                solver='adam',
                alpha=0.001,
                learning_rate='adaptive',
                max_iter=500,
                random_state=42
            )

            # Create voting ensemble
            self.model = VotingClassifier(
                estimators=[
                    ('rf', rf_model),
                    ('gb', gb_model),
                    ('mlp', mlp_model)
                ],
                voting='soft'
            )

            # Train the model
            logger.info("🔧 Training ensemble model...")
            self.model.fit(X_train, y_train)

            # Evaluate model
            train_score = self.model.score(X_train, y_train)
            test_score = self.model.score(X_test, y_test)

            logger.info(f"📊 Training accuracy: {train_score:.3f}")
            logger.info(f"📊 Testing accuracy: {test_score:.3f}")

            # Save the model
            self._save_model()

            # Update performance tracker
            self.performance_tracker['accuracy_history'].append({
                'timestamp': datetime.now().isoformat(),
                'train_accuracy': train_score,
                'test_accuracy': test_score
            })

            logger.info("✅ Model training completed successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Model training failed: {e}")
            return False

    def predict(self, symbol: str, timeframe_days: int = 30) -> Dict[str, Any]:
        """Make prediction with graceful error handling"""
        try:
            # Ensure model is available
            if self.model is None:
                logger.info("🎓 No model available, starting autonomous training...")
                if not self.autonomous_training():
                    return self._fallback_prediction(symbol, timeframe_days)

            # Extract features with error handling
            features = self.feature_extractor.extract_features(symbol, timeframe_days)

            if features is None:
                logger.warning(f"⚠️ Could not extract features for {symbol}, using fallback")
                return self._fallback_prediction(symbol, timeframe_days)

            # Prepare features for prediction
            try:
                # Ensure feature order matches training
                feature_values = []
                for name in self.feature_names:
                    feature_values.append(features.get(name, 0.0))

                # Scale features
                X = np.array([feature_values])
                X_scaled = self.scaler.transform(X)

                # Make prediction
                prediction_proba = self.model.predict_proba(X_scaled)[0]
                prediction_class = self.model.predict(X_scaled)[0]

                # Convert back to label
                predicted_label = self.label_encoder.inverse_transform([prediction_class])[0]
                confidence = float(max(prediction_proba)) * 100

                # Track prediction for self-learning
                self._track_prediction(symbol, predicted_label, confidence, timeframe_days)

                result = {
                    'symbol': symbol,
                    'prediction': predicted_label,
                    'confidence': confidence,
                    'timeframe_days': timeframe_days,
                    'model_version': self.performance_tracker['model_version'],
                    'timestamp': datetime.now().isoformat(),
                    'model_type': 'autonomous_neural_network'
                }

                logger.info(f"🎯 Prediction for {symbol}: {predicted_label} ({confidence:.1f}% confidence)")
                return result

            except Exception as e:
                logger.warning(f"⚠️ Prediction processing failed for {symbol}: {e}")
                return self._fallback_prediction(symbol, timeframe_days)

        except Exception as e:
            logger.error(f"❌ Prediction failed for {symbol}: {e}")
            return self._fallback_prediction(symbol, timeframe_days)

    def _fallback_prediction(self, symbol: str, timeframe_days: int) -> Dict[str, Any]:
        """Fallback prediction when neural network fails"""
        try:
            # Simple rule-based fallback
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="1mo")

            if len(hist) >= 5:
                # Calculate simple momentum
                current_price = hist['Close'].iloc[-1]
                avg_price = hist['Close'].rolling(5).mean().iloc[-1]

                if current_price > avg_price * 1.02:
                    prediction = 'BUY'
                    confidence = 60.0
                elif current_price < avg_price * 0.98:
                    prediction = 'SELL'
                    confidence = 60.0
                else:
                    prediction = 'HOLD'
                    confidence = 55.0
            else:
                prediction = 'HOLD'
                confidence = 50.0

        except:
            prediction = 'HOLD'
            confidence = 50.0

        return {
            'symbol': symbol,
            'prediction': prediction,
            'confidence': confidence,
            'timeframe_days': timeframe_days,
            'model_version': 0,
            'timestamp': datetime.now().isoformat(),
            'model_type': 'fallback_rule_based'
        }

    def _track_prediction(self, symbol: str, prediction: str, confidence: float, timeframe: int):
        """Track prediction for self-learning"""
        try:
            prediction_record = {
                'symbol': symbol,
                'prediction': prediction,
                'confidence': confidence,
                'timeframe': timeframe,
                'timestamp': datetime.now().isoformat(),
                'model_version': self.performance_tracker['model_version']
            }

            self.performance_tracker['predictions'].append(prediction_record)
            self.performance_tracker['total_predictions'] += 1

            # Keep only recent predictions (last 1000)
            if len(self.performance_tracker['predictions']) > 1000:
                self.performance_tracker['predictions'] = self.performance_tracker['predictions'][-1000:]

            self._save_performance_tracker()

        except Exception as e:
            logger.warning(f"⚠️ Could not track prediction: {e}")

    def _get_current_accuracy(self) -> float:
        """Get current model accuracy from performance tracker"""
        try:
            if not self.performance_tracker['accuracy_history']:
                return 0.0

            latest_accuracy = self.performance_tracker['accuracy_history'][-1]
            return latest_accuracy.get('test_accuracy', 0.0)

        except:
            return 0.0

    def get_model_status(self) -> Dict[str, Any]:
        """Get comprehensive model status"""
        return {
            'model_loaded': self.model is not None,
            'total_predictions': self.performance_tracker['total_predictions'],
            'model_version': self.performance_tracker['model_version'],
            'last_training': self.performance_tracker['last_training'],
            'current_accuracy': self._get_current_accuracy(),
            'discovered_stocks': len(self.stock_discovery.discovered_stocks),
            'feature_count': len(self.feature_names)
        }
