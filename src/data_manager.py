"""
Production-Grade Data Manager for StockTrek
Handles all database operations with robust error handling, data validation, and security
"""

import psycopg2
import psycopg2.extras
import json
import logging
import time
import uuid
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any, Union
from contextlib import contextmanager
import math

logger = logging.getLogger(__name__)

class DataManager:
    """
    Production-grade database manager with:
    - Robust error handling and retries
    - Data validation and sanitization
    - Connection pooling
    - Comprehensive schema management
    - API-ready data structures
    """

    def __init__(self):
        # Database configuration
        self.db_config = {
            'dbname': 'stocktrek_mainbranch',
            'user': 'stocktrek_admin',
            'password': 'equity_FR',
            'host': 'localhost',
            'port': '5432'
        }

        # Connection settings
        self.max_retries = 3
        self.retry_delay = 1
        self.connection_timeout = 30

        # Initialize database
        self._initialize_database_with_retry()

    def _initialize_database_with_retry(self):
        """Initialize database with retry logic"""
        for attempt in range(self.max_retries + 1):
            try:
                self._initialize_database()
                logger.info("✅ Database initialized successfully")
                return
            except Exception as e:
                if attempt < self.max_retries:
                    logger.warning(f"⚠️ Database init attempt {attempt + 1} failed, retrying...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"❌ Database initialization failed after {self.max_retries + 1} attempts")
                    logger.warning("⚠️ System will continue without database functionality")
                    break

    @contextmanager
    def get_connection(self):
        """Robust database connection with retry logic"""
        conn = None
        for attempt in range(self.max_retries + 1):
            try:
                conn = psycopg2.connect(
                    **self.db_config,
                    connect_timeout=self.connection_timeout
                )
                conn.autocommit = False
                yield conn
                return
            except psycopg2.OperationalError as e:
                if conn:
                    try:
                        conn.close()
                    except:
                        pass
                    conn = None

                if attempt < self.max_retries:
                    logger.warning(f"⚠️ Connection attempt {attempt + 1} failed, retrying...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"❌ Database connection failed: {e}")
                    raise
            except Exception as e:
                if conn:
                    try:
                        conn.rollback()
                        conn.close()
                    except:
                        pass
                logger.error(f"❌ Database error: {e}")
                raise

        if conn:
            try:
                conn.close()
            except:
                pass

    def _initialize_database(self):
        """Initialize comprehensive database schema"""
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                # Test connection
                cur.execute("SELECT version();")
                version = cur.fetchone()[0]
                logger.info(f"📊 Connected to PostgreSQL: {version}")

                # Create all tables
                self._create_predictions_table(cur)
                self._create_backtest_results_table(cur)
                self._create_model_performance_table(cur)
                self._create_daemon_status_table(cur)
                self._create_api_logs_table(cur)

                # Create indices
                self._create_indices(cur)

                # Create functions and triggers
                self._create_database_functions(cur)

                conn.commit()
                logger.info("✅ Database schema created successfully")

    def _create_predictions_table(self, cur):
        """Create robust predictions table"""
        cur.execute("""
            CREATE TABLE IF NOT EXISTS predictions (
                id SERIAL PRIMARY KEY,
                ticker VARCHAR(20) NOT NULL CHECK (LENGTH(ticker) > 0),
                timeframe_days INTEGER NOT NULL CHECK (timeframe_days > 0 AND timeframe_days <= 365),
                prediction VARCHAR(20) NOT NULL CHECK (prediction IN ('BUY', 'SELL', 'HOLD')),
                confidence FLOAT NOT NULL CHECK (confidence >= 0 AND confidence <= 100),
                current_price FLOAT CHECK (current_price > 0),
                target_price FLOAT CHECK (target_price > 0),
                expected_return FLOAT,
                features JSONB DEFAULT '{}' NOT NULL,
                price_targets JSONB DEFAULT '{}' NOT NULL,
                technical_analysis JSONB DEFAULT '{}' NOT NULL,
                fundamental_analysis JSONB DEFAULT '{}' NOT NULL,
                model_version VARCHAR(50) DEFAULT 'v1.0' NOT NULL,
                prediction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                actual_outcome VARCHAR(20) CHECK (actual_outcome IN ('BUY', 'SELL', 'HOLD') OR actual_outcome IS NULL),
                actual_return FLOAT,
                is_correct BOOLEAN,
                validated_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
            );
        """)

    def _create_backtest_results_table(self, cur):
        """Create backtest results table"""
        cur.execute("""
            CREATE TABLE IF NOT EXISTS backtest_results (
                id SERIAL PRIMARY KEY,
                ticker VARCHAR(20) NOT NULL CHECK (LENGTH(ticker) > 0),
                timeframe_days INTEGER NOT NULL CHECK (timeframe_days > 0),
                start_date DATE NOT NULL,
                end_date DATE NOT NULL CHECK (end_date >= start_date),
                total_predictions INTEGER NOT NULL CHECK (total_predictions >= 0),
                correct_predictions INTEGER NOT NULL CHECK (correct_predictions >= 0 AND correct_predictions <= total_predictions),
                accuracy FLOAT NOT NULL CHECK (accuracy >= 0 AND accuracy <= 100),
                total_return FLOAT,
                sharpe_ratio FLOAT,
                max_drawdown FLOAT CHECK (max_drawdown <= 0),
                volatility FLOAT CHECK (volatility >= 0),
                results_data JSONB DEFAULT '{}' NOT NULL,
                model_version VARCHAR(50) DEFAULT 'v1.0' NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
            );
        """)

    def _create_model_performance_table(self, cur):
        """Create model performance tracking table"""
        cur.execute("""
            CREATE TABLE IF NOT EXISTS model_performance (
                id SERIAL PRIMARY KEY,
                model_version VARCHAR(50) NOT NULL,
                accuracy FLOAT NOT NULL CHECK (accuracy >= 0 AND accuracy <= 100),
                precision_score FLOAT CHECK (precision_score >= 0 AND precision_score <= 1),
                recall_score FLOAT CHECK (recall_score >= 0 AND recall_score <= 1),
                f1_score FLOAT CHECK (f1_score >= 0 AND f1_score <= 1),
                training_samples INTEGER CHECK (training_samples > 0),
                validation_samples INTEGER CHECK (validation_samples > 0),
                feature_count INTEGER CHECK (feature_count > 0),
                training_duration_seconds FLOAT CHECK (training_duration_seconds > 0),
                validation_data JSONB DEFAULT '{}' NOT NULL,
                hyperparameters JSONB DEFAULT '{}' NOT NULL,
                is_active BOOLEAN DEFAULT TRUE NOT NULL,
                training_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
            );
        """)

    def _create_daemon_status_table(self, cur):
        """Create daemon status tracking table"""
        cur.execute("""
            CREATE TABLE IF NOT EXISTS daemon_status (
                id SERIAL PRIMARY KEY,
                daemon_id VARCHAR(100) UNIQUE NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'stopped' CHECK (status IN ('running', 'stopped', 'error', 'paused')),
                start_time TIMESTAMP,
                last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                predictions_made INTEGER DEFAULT 0 CHECK (predictions_made >= 0),
                backtests_run INTEGER DEFAULT 0 CHECK (backtests_run >= 0),
                errors_count INTEGER DEFAULT 0 CHECK (errors_count >= 0),
                uptime_seconds INTEGER DEFAULT 0 CHECK (uptime_seconds >= 0),
                config_data JSONB DEFAULT '{}' NOT NULL,
                performance_metrics JSONB DEFAULT '{}' NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
            );
        """)

    def _create_api_logs_table(self, cur):
        """Create API access logs table for monitoring"""
        cur.execute("""
            CREATE TABLE IF NOT EXISTS api_logs (
                id SERIAL PRIMARY KEY,
                endpoint VARCHAR(200) NOT NULL,
                method VARCHAR(10) NOT NULL,
                request_data JSONB DEFAULT '{}',
                response_data JSONB DEFAULT '{}',
                status_code INTEGER,
                response_time_ms FLOAT,
                user_agent TEXT,
                ip_address INET,
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
            );
        """)

    def _create_indices(self, cur):
        """Create optimized database indices"""
        indices = [
            # Predictions table indices
            "CREATE INDEX IF NOT EXISTS idx_predictions_ticker ON predictions(ticker);",
            "CREATE INDEX IF NOT EXISTS idx_predictions_date ON predictions(prediction_date);",
            "CREATE INDEX IF NOT EXISTS idx_predictions_timeframe ON predictions(timeframe_days);",
            "CREATE INDEX IF NOT EXISTS idx_predictions_ticker_timeframe ON predictions(ticker, timeframe_days);",
            "CREATE INDEX IF NOT EXISTS idx_predictions_validation ON predictions(is_correct) WHERE is_correct IS NOT NULL;",

            # Backtest results indices
            "CREATE INDEX IF NOT EXISTS idx_backtest_ticker ON backtest_results(ticker);",
            "CREATE INDEX IF NOT EXISTS idx_backtest_date ON backtest_results(created_at);",
            "CREATE INDEX IF NOT EXISTS idx_backtest_accuracy ON backtest_results(accuracy);",

            # Model performance indices
            "CREATE INDEX IF NOT EXISTS idx_model_performance_active ON model_performance(is_active);",
            "CREATE INDEX IF NOT EXISTS idx_model_performance_version ON model_performance(model_version);",

            # Daemon status indices
            "CREATE INDEX IF NOT EXISTS idx_daemon_status_id ON daemon_status(daemon_id);",
            "CREATE INDEX IF NOT EXISTS idx_daemon_heartbeat ON daemon_status(last_heartbeat);",

            # API logs indices
            "CREATE INDEX IF NOT EXISTS idx_api_logs_endpoint ON api_logs(endpoint);",
            "CREATE INDEX IF NOT EXISTS idx_api_logs_date ON api_logs(created_at);",
        ]

        for index_sql in indices:
            try:
                cur.execute(index_sql)
            except Exception as e:
                logger.warning(f"⚠️ Failed to create index: {e}")

    def _create_database_functions(self, cur):
        """Create database functions and triggers"""
        # Update timestamp trigger function
        cur.execute("""
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ language 'plpgsql';
        """)

        # Create triggers for updated_at columns
        triggers = [
            "predictions",
            "daemon_status"
        ]

        for table in triggers:
            cur.execute(f"""
                DROP TRIGGER IF EXISTS update_{table}_updated_at ON {table};
                CREATE TRIGGER update_{table}_updated_at
                    BEFORE UPDATE ON {table}
                    FOR EACH ROW
                    EXECUTE FUNCTION update_updated_at_column();
            """)
    
    def validate_and_clean_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and sanitize data before database operations"""
        if not isinstance(data, dict):
            raise ValueError("Data must be a dictionary")

        cleaned_data = {}
        for key, value in data.items():
            # Handle None values
            if value is None:
                cleaned_data[key] = None
                continue

            # Handle numeric values
            if isinstance(value, (int, float)):
                if math.isnan(value) or math.isinf(value):
                    cleaned_data[key] = None
                else:
                    cleaned_data[key] = float(value) if isinstance(value, float) else int(value)
                continue

            # Handle strings
            if isinstance(value, str):
                cleaned_value = value.strip()
                if len(cleaned_value) == 0:
                    cleaned_data[key] = None
                else:
                    cleaned_data[key] = cleaned_value
                continue

            # Handle JSON serializable objects
            try:
                json.dumps(value)
                cleaned_data[key] = value
            except (TypeError, ValueError):
                logger.warning(f"⚠️ Skipping non-serializable value for key {key}")
                cleaned_data[key] = None

        return cleaned_data

    def store_prediction(self, ticker: str, timeframe_days: int, prediction_data: Dict[str, Any]) -> Optional[int]:
        """
        Store prediction results with comprehensive validation and error handling
        Returns prediction ID if successful, None if failed
        """
        try:
            # Validate inputs
            if not ticker or not isinstance(ticker, str):
                raise ValueError("Ticker must be a non-empty string")

            if not isinstance(timeframe_days, int) or timeframe_days <= 0:
                raise ValueError("Timeframe days must be a positive integer")

            # Clean and validate prediction data
            cleaned_data = self.validate_and_clean_data(prediction_data)

            # Ensure required fields
            required_fields = ['prediction', 'confidence']
            for field in required_fields:
                if field not in cleaned_data or cleaned_data[field] is None:
                    raise ValueError(f"Required field '{field}' is missing or None")

            # Validate prediction value
            valid_predictions = ['BUY', 'SELL', 'HOLD']
            if cleaned_data['prediction'].upper() not in valid_predictions:
                raise ValueError(f"Prediction must be one of: {valid_predictions}")

            # Validate confidence
            confidence = cleaned_data['confidence']
            if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 100:
                raise ValueError("Confidence must be between 0 and 100")

            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    # Prepare data for insertion
                    price_targets = cleaned_data.get('price_targets', {})
                    technical_analysis = cleaned_data.get('technical_analysis', {})
                    fundamental_analysis = cleaned_data.get('fundamental_analysis', {})
                    features = cleaned_data.get('features', {})

                    insert_query = """
                    INSERT INTO predictions (
                        ticker, timeframe_days, prediction, confidence,
                        current_price, target_price, expected_return,
                        features, price_targets, technical_analysis, fundamental_analysis,
                        model_version, prediction_date
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                    """

                    values = (
                        ticker.upper(),
                        timeframe_days,
                        cleaned_data['prediction'].upper(),
                        confidence,
                        price_targets.get('current_price'),
                        price_targets.get('target_price'),
                        price_targets.get('expected_return'),
                        json.dumps(features),
                        json.dumps(price_targets),
                        json.dumps(technical_analysis),
                        json.dumps(fundamental_analysis),
                        cleaned_data.get('model_version', 'v1.0'),
                        datetime.now()
                    )

                    cur.execute(insert_query, values)
                    prediction_id = cur.fetchone()[0]
                    conn.commit()

                    logger.info(f"✅ Stored prediction {prediction_id} for {ticker}")
                    return prediction_id

        except Exception as e:
            logger.error(f"❌ Error storing prediction: {e}")
            return None
    
    def store_backtest_result(self, ticker: str, timeframe_days: int, backtest_data: Dict[str, Any]) -> Optional[int]:
        """Store backtest results with validation"""
        try:
            # Validate inputs
            if not ticker or not isinstance(ticker, str):
                raise ValueError("Ticker must be a non-empty string")

            cleaned_data = self.validate_and_clean_data(backtest_data)

            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    insert_query = """
                    INSERT INTO backtest_results (
                        ticker, timeframe_days, start_date, end_date,
                        total_predictions, correct_predictions, accuracy,
                        total_return, sharpe_ratio, max_drawdown, volatility,
                        results_data, model_version
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                    """

                    values = (
                        ticker.upper(),
                        timeframe_days,
                        cleaned_data.get('start_date'),
                        cleaned_data.get('end_date'),
                        cleaned_data.get('total_predictions', 0),
                        cleaned_data.get('correct_predictions', 0),
                        cleaned_data.get('accuracy', 0.0),
                        cleaned_data.get('total_return'),
                        cleaned_data.get('sharpe_ratio'),
                        cleaned_data.get('max_drawdown'),
                        cleaned_data.get('volatility'),
                        json.dumps(cleaned_data),
                        cleaned_data.get('model_version', 'v1.0')
                    )

                    cur.execute(insert_query, values)
                    backtest_id = cur.fetchone()[0]
                    conn.commit()

                    logger.info(f"✅ Stored backtest result {backtest_id} for {ticker}")
                    return backtest_id

        except Exception as e:
            logger.error(f"❌ Error storing backtest result: {e}")
            return None
    
    def get_recent_predictions(self, ticker: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent predictions from the database"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    if ticker:
                        query = """
                        SELECT * FROM predictions 
                        WHERE ticker = %s 
                        ORDER BY created_at DESC 
                        LIMIT %s
                        """
                        cur.execute(query, (ticker.upper(), limit))
                    else:
                        query = """
                        SELECT * FROM predictions 
                        ORDER BY created_at DESC 
                        LIMIT %s
                        """
                        cur.execute(query, (limit,))
                    
                    columns = [desc[0] for desc in cur.description]
                    results = []
                    
                    for row in cur.fetchall():
                        result = dict(zip(columns, row))
                        # Parse JSON data
                        if result.get('prediction_data'):
                            try:
                                result['prediction_data'] = json.loads(result['prediction_data'])
                            except json.JSONDecodeError:
                                pass
                        results.append(result)
                    
                    return results
                    
        except Exception as e:
            logger.error(f"❌ Error getting recent predictions: {e}")
            return []
    
    def get_model_performance_stats(self) -> Dict[str, Any]:
        """Get overall model performance statistics"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    # Get overall accuracy from backtest results
                    cur.execute("""
                        SELECT 
                            AVG(accuracy) as avg_accuracy,
                            COUNT(*) as total_backtests,
                            AVG(avg_return) as avg_return,
                            MIN(max_drawdown) as best_max_drawdown
                        FROM backtest_results
                        WHERE created_at >= NOW() - INTERVAL '30 days'
                    """)
                    
                    result = cur.fetchone()
                    if result:
                        return {
                            'avg_accuracy': float(result[0]) if result[0] else 0,
                            'total_backtests': result[1],
                            'avg_return': float(result[2]) if result[2] else 0,
                            'best_max_drawdown': float(result[3]) if result[3] else 0
                        }
                    else:
                        return {
                            'avg_accuracy': 0,
                            'total_backtests': 0,
                            'avg_return': 0,
                            'best_max_drawdown': 0
                        }
                        
        except Exception as e:
            logger.error(f"❌ Error getting model performance stats: {e}")
            return {}
    
    def get_company_data(self, ticker: str) -> Optional[Dict[str, Any]]:
        """Get company data from the database"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    query = """
                    SELECT c.*, cd.* 
                    FROM companies c
                    LEFT JOIN company_data cd ON c.id = cd.company_id
                    WHERE c.ticker = %s
                    ORDER BY cd.as_of_date DESC
                    LIMIT 1
                    """
                    
                    cur.execute(query, (ticker.upper(),))
                    result = cur.fetchone()
                    
                    if result:
                        columns = [desc[0] for desc in cur.description]
                        data = dict(zip(columns, result))
                        
                        # Parse JSON fields
                        json_fields = ['revenue', 'ebitda', 'depreciation_amortization', 
                                     'ebit', 'capex', 'working_capital', 'levered_fcf', 'net_income']
                        
                        for field in json_fields:
                            if data.get(field):
                                try:
                                    data[field] = json.loads(data[field])
                                except json.JSONDecodeError:
                                    pass
                        
                        return data
                    else:
                        return None
                        
        except Exception as e:
            logger.error(f"❌ Error getting company data for {ticker}: {e}")
            return None
    
    def update_database_schema(self):
        """Update database schema to support new features"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    # Create predictions table if it doesn't exist
                    cur.execute("""
                    CREATE TABLE IF NOT EXISTS predictions (
                        id SERIAL PRIMARY KEY,
                        ticker VARCHAR(10) NOT NULL,
                        timeframe_days INTEGER NOT NULL,
                        prediction VARCHAR(10) NOT NULL,
                        confidence NUMERIC(5,2),
                        current_price NUMERIC(10,2),
                        target_price NUMERIC(10,2),
                        expected_return_pct NUMERIC(6,2),
                        prediction_data JSONB,
                        model_type VARCHAR(50),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        actual_outcome VARCHAR(10),
                        outcome_date TIMESTAMP,
                        outcome_return_pct NUMERIC(6,2)
                    )
                    """)
                    
                    # Create backtest_results table if it doesn't exist
                    cur.execute("""
                    CREATE TABLE IF NOT EXISTS backtest_results (
                        id SERIAL PRIMARY KEY,
                        ticker VARCHAR(10) NOT NULL,
                        timeframe_days INTEGER NOT NULL,
                        accuracy NUMERIC(5,2),
                        total_predictions INTEGER,
                        correct_predictions INTEGER,
                        avg_return NUMERIC(6,2),
                        max_drawdown NUMERIC(6,2),
                        backtest_data JSONB,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                    """)
                    
                    # Create model_performance table if it doesn't exist
                    cur.execute("""
                    CREATE TABLE IF NOT EXISTS model_performance (
                        id SERIAL PRIMARY KEY,
                        model_version VARCHAR(50),
                        accuracy NUMERIC(5,2),
                        precision_score NUMERIC(5,2),
                        recall_score NUMERIC(5,2),
                        f1_score NUMERIC(5,2),
                        training_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        performance_data JSONB
                    )
                    """)
                    
                    # Create indexes for better performance
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_predictions_ticker ON predictions(ticker)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_predictions_created_at ON predictions(created_at)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_backtest_ticker ON backtest_results(ticker)")
                    
                    conn.commit()
                    logger.info("✅ Database schema updated successfully")
                    
        except Exception as e:
            logger.error(f"❌ Error updating database schema: {e}")
            raise

    def get_prediction_accuracy(self, ticker: str = None, days: int = 30) -> Dict[str, Any]:
        """Get prediction accuracy statistics for API"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    base_query = """
                    SELECT
                        COUNT(*) as total_predictions,
                        COUNT(CASE WHEN is_correct = true THEN 1 END) as correct_predictions,
                        AVG(CASE WHEN is_correct = true THEN 1.0 ELSE 0.0 END) * 100 as accuracy,
                        AVG(actual_return) as avg_return
                    FROM predictions
                    WHERE is_correct IS NOT NULL
                    AND prediction_date >= %s
                    """

                    params = [datetime.now() - timedelta(days=days)]

                    if ticker:
                        base_query += " AND ticker = %s"
                        params.append(ticker.upper())

                    cur.execute(base_query, params)
                    result = cur.fetchone()

                    if result:
                        return {
                            'total_predictions': result[0] or 0,
                            'correct_predictions': result[1] or 0,
                            'accuracy': float(result[2] or 0),
                            'avg_return': float(result[3] or 0)
                        }
                    else:
                        return {
                            'total_predictions': 0,
                            'correct_predictions': 0,
                            'accuracy': 0.0,
                            'avg_return': 0.0
                        }

        except Exception as e:
            logger.error(f"❌ Error calculating accuracy: {e}")
            return {
                'total_predictions': 0,
                'correct_predictions': 0,
                'accuracy': 0.0,
                'avg_return': 0.0
            }

    def update_daemon_status(self, daemon_id: str, status_data: Dict[str, Any]) -> bool:
        """Update daemon status for monitoring"""
        try:
            cleaned_data = self.validate_and_clean_data(status_data)

            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    # Upsert daemon status
                    upsert_query = """
                    INSERT INTO daemon_status (
                        daemon_id, status, start_time, last_heartbeat,
                        predictions_made, backtests_run, errors_count,
                        uptime_seconds, config_data, performance_metrics
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (daemon_id) DO UPDATE SET
                        status = EXCLUDED.status,
                        last_heartbeat = EXCLUDED.last_heartbeat,
                        predictions_made = EXCLUDED.predictions_made,
                        backtests_run = EXCLUDED.backtests_run,
                        errors_count = EXCLUDED.errors_count,
                        uptime_seconds = EXCLUDED.uptime_seconds,
                        performance_metrics = EXCLUDED.performance_metrics,
                        updated_at = CURRENT_TIMESTAMP
                    """

                    values = (
                        daemon_id,
                        cleaned_data.get('status', 'unknown'),
                        cleaned_data.get('start_time'),
                        datetime.now(),
                        cleaned_data.get('predictions_made', 0),
                        cleaned_data.get('backtests_run', 0),
                        cleaned_data.get('errors_count', 0),
                        cleaned_data.get('uptime_seconds', 0),
                        json.dumps(cleaned_data.get('config_data', {})),
                        json.dumps(cleaned_data.get('performance_metrics', {}))
                    )

                    cur.execute(upsert_query, values)
                    conn.commit()

                    return True

        except Exception as e:
            logger.error(f"❌ Error updating daemon status: {e}")
            return False

    def get_system_health(self) -> Dict[str, Any]:
        """Get comprehensive system health metrics for API"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    # Get prediction statistics
                    cur.execute("""
                        SELECT
                            COUNT(*) as total_predictions,
                            COUNT(CASE WHEN prediction_date >= NOW() - INTERVAL '24 hours' THEN 1 END) as predictions_24h,
                            COUNT(CASE WHEN is_correct = true THEN 1 END) as correct_predictions,
                            AVG(CASE WHEN is_correct = true THEN 1.0 ELSE 0.0 END) * 100 as overall_accuracy
                        FROM predictions
                    """)
                    pred_stats = cur.fetchone()

                    # Get daemon statistics
                    cur.execute("""
                        SELECT
                            COUNT(*) as total_daemons,
                            COUNT(CASE WHEN status = 'running' THEN 1 END) as running_daemons,
                            COUNT(CASE WHEN last_heartbeat >= NOW() - INTERVAL '5 minutes' THEN 1 END) as active_daemons
                        FROM daemon_status
                    """)
                    daemon_stats = cur.fetchone()

                    return {
                        'database_connected': True,
                        'predictions': {
                            'total': pred_stats[0] if pred_stats else 0,
                            'last_24h': pred_stats[1] if pred_stats else 0,
                            'correct': pred_stats[2] if pred_stats else 0,
                            'accuracy': float(pred_stats[3] or 0) if pred_stats else 0.0
                        },
                        'daemons': {
                            'total': daemon_stats[0] if daemon_stats else 0,
                            'running': daemon_stats[1] if daemon_stats else 0,
                            'active': daemon_stats[2] if daemon_stats else 0
                        },
                        'timestamp': datetime.now().isoformat()
                    }

        except Exception as e:
            logger.error(f"❌ Error getting system health: {e}")
            return {
                'database_connected': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def close_connection(self):
        """Close database connection (if needed for cleanup)"""
        # Context manager handles connection cleanup automatically
        pass
