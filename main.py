#!/usr/bin/env python3
"""
StockTrek - Production Stock Prediction System
Main application with CLI interface and daemon mode

Usage:
    python3 main.py --predict TICKER --timeframe DAYS
    python3 main.py --daemon
    python3 main.py --backtest TICKER --days DAYS
    python3 main.py --train
"""

import argparse
import sys
import os
import logging
import signal
import time
import threading
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import our modules
from src.neural_network import NeuralNetworkPredictor
from src.data_manager import DataManager
from src.daemon import StockTrekDaemon
from src.backtesting import BacktestEngine
from ETL.etl import etl_for_ticker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stocktrek.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class StockTrekApp:
    """Main StockTrek application class"""
    
    def __init__(self):
        self.neural_network = None
        self.data_manager = None
        self.daemon = None
        self.backtest_engine = None
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize all application components"""
        try:
            logger.info("Initializing StockTrek components...")
            
            # Initialize data manager
            self.data_manager = DataManager()
            
            # Initialize neural network predictor
            self.neural_network = NeuralNetworkPredictor()
            
            # Initialize backtest engine
            self.backtest_engine = BacktestEngine(self.neural_network, self.data_manager)
            
            # Initialize daemon (but don't start it yet)
            self.daemon = StockTrekDaemon(self.neural_network, self.data_manager, self.backtest_engine)
            
            logger.info("✅ All components initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize components: {e}")
            raise
    
    def predict_stock(self, ticker: str, timeframe_days: int):
        """Make a prediction for a specific stock and timeframe"""
        try:
            logger.info(f"🔮 Making prediction for {ticker} with {timeframe_days} day timeframe")
            
            # First, update data for the ticker
            logger.info(f"📊 Updating data for {ticker}...")
            etl_for_ticker(ticker)
            
            # Make prediction using neural network
            prediction = self.neural_network.predict_stock_movement(ticker, timeframe_days)
            
            if 'error' in prediction:
                logger.error(f"❌ Prediction failed: {prediction['error']}")
                return False
            
            # Display prediction results
            self._display_prediction_results(ticker, timeframe_days, prediction)
            
            # Store prediction in database
            self.data_manager.store_prediction(ticker, timeframe_days, prediction)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error making prediction for {ticker}: {e}")
            return False
    
    def _display_prediction_results(self, ticker: str, timeframe_days: int, prediction: dict):
        """Display formatted prediction results"""
        print("\n" + "="*60)
        print(f"🎯 STOCKTREK PREDICTION RESULTS")
        print("="*60)
        print(f"📈 Ticker: {ticker.upper()}")
        print(f"⏰ Timeframe: {timeframe_days} days")
        print(f"📅 Analysis Date: {prediction.get('analysis_date', 'N/A')}")
        print("-"*60)
        
        # Main prediction
        pred_label = prediction.get('prediction', 'N/A')
        confidence = prediction.get('confidence', 0)
        
        # Emoji based on prediction
        emoji = "🚀" if pred_label == "BUY" else "⚠️" if pred_label == "HOLD" else "📉"
        
        print(f"{emoji} Prediction: {pred_label}")
        print(f"🎯 Confidence: {confidence:.1f}%")
        
        # Price targets
        if 'price_targets' in prediction:
            targets = prediction['price_targets']
            print(f"💰 Current Price: ${targets.get('current_price', 'N/A'):.2f}")
            print(f"🎯 Target Price: ${targets.get('target_price', 'N/A'):.2f}")
            print(f"📊 Expected Return: {targets.get('expected_return_pct', 'N/A'):.1f}%")
        
        # Model info
        prob_data = prediction.get('prediction_probabilities', {})
        certainty = prob_data.get('model_certainty', 'Unknown')
        print(f"🤖 Model Certainty: {certainty}")
        
        print("="*60)
    
    def run_backtest(self, ticker: str, days: int):
        """Run backtest for a specific ticker and timeframe"""
        try:
            logger.info(f"🧪 Running backtest for {ticker} over {days} days")
            
            results = self.backtest_engine.run_backtest(ticker, days)
            
            if results:
                self._display_backtest_results(ticker, days, results)
                return True
            else:
                logger.error("❌ Backtest failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error running backtest: {e}")
            return False
    
    def _display_backtest_results(self, ticker: str, days: int, results: dict):
        """Display formatted backtest results"""
        print("\n" + "="*60)
        print(f"🧪 BACKTEST RESULTS")
        print("="*60)
        print(f"📈 Ticker: {ticker.upper()}")
        print(f"📅 Period: {days} days")
        print("-"*60)
        
        accuracy = results.get('accuracy', 0)
        total_predictions = results.get('total_predictions', 0)
        correct_predictions = results.get('correct_predictions', 0)
        
        print(f"🎯 Accuracy: {accuracy:.1f}%")
        print(f"📊 Total Predictions: {total_predictions}")
        print(f"✅ Correct Predictions: {correct_predictions}")
        print(f"❌ Incorrect Predictions: {total_predictions - correct_predictions}")
        
        if 'performance_metrics' in results:
            metrics = results['performance_metrics']
            print(f"📈 Average Return: {metrics.get('avg_return', 0):.2f}%")
            print(f"📉 Max Drawdown: {metrics.get('max_drawdown', 0):.2f}%")
        
        print("="*60)
    
    def train_model(self):
        """Train or retrain the neural network model"""
        try:
            logger.info("🎓 Starting neural network training...")
            
            success = self.neural_network.train_model()
            
            if success:
                logger.info("✅ Model training completed successfully")
                return True
            else:
                logger.error("❌ Model training failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error during model training: {e}")
            return False
    
    def start_daemon(self):
        """Start the daemon mode for autonomous operation"""
        try:
            logger.info("🤖 Starting StockTrek daemon mode...")
            
            # Set up signal handlers for graceful shutdown
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            # Start the daemon
            self.daemon.start()
            
            logger.info("✅ Daemon started successfully. Press Ctrl+C to stop.")
            
            # Keep the main thread alive
            try:
                while self.daemon.is_running():
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("🛑 Received interrupt signal")
            
            self.daemon.stop()
            logger.info("✅ Daemon stopped gracefully")
            
        except Exception as e:
            logger.error(f"❌ Error in daemon mode: {e}")
            return False
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"🛑 Received signal {signum}, shutting down...")
        if self.daemon:
            self.daemon.stop()

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="StockTrek - Production Stock Prediction System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 main.py --predict AAPL --timeframe 30
  python3 main.py --daemon
  python3 main.py --backtest TSLA --days 90
  python3 main.py --train
        """
    )
    
    # Prediction mode
    parser.add_argument('--predict', type=str, metavar='TICKER',
                       help='Make prediction for specific ticker')
    parser.add_argument('--timeframe', type=int, metavar='DAYS', default=30,
                       help='Prediction timeframe in days (default: 30)')
    
    # Daemon mode
    parser.add_argument('--daemon', action='store_true',
                       help='Run in daemon mode for autonomous operation')
    
    # Backtest mode
    parser.add_argument('--backtest', type=str, metavar='TICKER',
                       help='Run backtest for specific ticker')
    parser.add_argument('--days', type=int, metavar='DAYS', default=90,
                       help='Backtest period in days (default: 90)')
    
    # Training mode
    parser.add_argument('--train', action='store_true',
                       help='Train or retrain the neural network model')
    
    # Logging level
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Set logging level (default: INFO)')
    
    return parser.parse_args()

def main():
    """Main application entry point"""
    print("🚀 StockTrek - Production Stock Prediction System")
    print("=" * 50)
    
    # Parse arguments
    args = parse_arguments()
    
    # Set logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    try:
        # Initialize application
        app = StockTrekApp()
        
        # Route to appropriate functionality
        if args.predict:
            success = app.predict_stock(args.predict, args.timeframe)
            sys.exit(0 if success else 1)
            
        elif args.daemon:
            app.start_daemon()
            
        elif args.backtest:
            success = app.run_backtest(args.backtest, args.days)
            sys.exit(0 if success else 1)
            
        elif args.train:
            success = app.train_model()
            sys.exit(0 if success else 1)
            
        else:
            print("❌ No action specified. Use --help for usage information.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("🛑 Application interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Application error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
