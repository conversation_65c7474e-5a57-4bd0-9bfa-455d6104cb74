#!/usr/bin/env python3
"""
StockTrek Live System Test
Tests the complete system with LIVE, UP-TO-DATE data
"""

import sys
import os
import logging
import time
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_live_data_fetch():
    """Test fetching live, up-to-date market data"""
    print("🌐 Testing LIVE data fetch...")
    
    try:
        import yfinance as yf
        import pandas as pd
        
        # Test multiple tickers to ensure robustness
        test_tickers = ['AAPL', 'MSFT', 'GOOGL', 'TSLA']
        
        for ticker in test_tickers:
            print(f"  📈 Testing {ticker}...")
            
            stock = yf.Ticker(ticker)
            
            # Get recent data
            hist = stock.history(period="5d")
            info = stock.info
            
            if hist.empty:
                print(f"  ❌ No historical data for {ticker}")
                return False
            
            # Verify data is recent (within last 7 days)
            latest_date = hist.index[-1].date()
            days_old = (datetime.now().date() - latest_date).days
            
            if days_old > 7:
                print(f"  ⚠️ Data for {ticker} is {days_old} days old")
            else:
                print(f"  ✅ Fresh data for {ticker} (last updated: {latest_date})")
            
            # Display current info
            current_price = hist['Close'].iloc[-1]
            company_name = info.get('longName', 'N/A')
            market_cap = info.get('marketCap', 'N/A')
            
            print(f"    Company: {company_name}")
            print(f"    Current Price: ${current_price:.2f}")
            print(f"    Market Cap: {market_cap}")
            
            time.sleep(1)  # Rate limiting
        
        print("✅ Live data fetch test PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Live data fetch test FAILED: {e}")
        return False

def test_neural_network_with_live_data():
    """Test neural network with live data"""
    print("\n🧠 Testing Neural Network with LIVE data...")
    
    try:
        from src.neural_network import NeuralNetworkPredictor
        
        # Initialize predictor
        predictor = NeuralNetworkPredictor()
        print("  ✅ Neural network initialized")
        
        # Test with live data
        test_ticker = 'AAPL'
        print(f"  🔮 Testing prediction for {test_ticker} with live data...")
        
        # Extract features with live data
        features = predictor.extract_comprehensive_features(test_ticker, timeframe_days=30)
        
        if not features:
            print("  ❌ Failed to extract features from live data")
            return False
        
        print(f"  ✅ Extracted {len(features)} features from live data")
        
        # Display some key features
        key_features = ['current_price', 'rsi', 'price_momentum', 'volatility']
        for feature in key_features:
            if feature in features:
                print(f"    {feature}: {features[feature]:.3f}")
        
        # Make prediction
        prediction = predictor.predict_stock_movement(test_ticker, 30)
        
        if 'error' in prediction:
            print(f"  ❌ Prediction failed: {prediction['error']}")
            return False
        
        print("  ✅ Prediction generated successfully")
        print(f"    Prediction: {prediction.get('prediction', 'N/A')}")
        print(f"    Confidence: {prediction.get('confidence', 0):.1f}%")
        print(f"    Target Price: ${prediction.get('price_targets', {}).get('target_price', 0):.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Neural network test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_etl_with_live_data():
    """Test ETL pipeline with live data"""
    print("\n📊 Testing ETL pipeline with LIVE data...")
    
    try:
        from ETL.etl import etl_for_ticker
        
        test_ticker = 'MSFT'
        print(f"  🔄 Running ETL for {test_ticker}...")
        
        # Run ETL process
        etl_for_ticker(test_ticker)
        
        print(f"  ✅ ETL completed for {test_ticker}")
        return True
        
    except Exception as e:
        print(f"❌ ETL test FAILED: {e}")
        return False

def test_prediction_accuracy():
    """Test prediction with multiple timeframes"""
    print("\n🎯 Testing prediction accuracy with multiple timeframes...")
    
    try:
        from src.neural_network import NeuralNetworkPredictor
        
        predictor = NeuralNetworkPredictor()
        test_ticker = 'GOOGL'
        timeframes = [7, 14, 30, 60]
        
        for timeframe in timeframes:
            print(f"  📅 Testing {timeframe}-day prediction for {test_ticker}...")
            
            prediction = predictor.predict_stock_movement(test_ticker, timeframe)
            
            if 'error' in prediction:
                print(f"    ❌ {timeframe}-day prediction failed")
                continue
            
            pred_label = prediction.get('prediction', 'N/A')
            confidence = prediction.get('confidence', 0)
            
            print(f"    ✅ {timeframe}-day: {pred_label} ({confidence:.1f}%)")
            
            time.sleep(2)  # Rate limiting
        
        return True
        
    except Exception as e:
        print(f"❌ Prediction accuracy test FAILED: {e}")
        return False

def test_cli_interface():
    """Test CLI interface with live data"""
    print("\n💻 Testing CLI interface with LIVE data...")
    
    try:
        import subprocess
        
        # Test help command
        result = subprocess.run([sys.executable, "main.py", "--help"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            print("  ❌ CLI help command failed")
            return False
        
        print("  ✅ CLI help command works")
        
        # Test prediction command (this will use live data)
        print("  🔮 Testing live prediction command...")
        result = subprocess.run([sys.executable, "main.py", "--predict", "AAPL", "--timeframe", "30"], 
                              capture_output=True, text=True, timeout=120)
        
        if result.returncode != 0:
            print(f"  ❌ CLI prediction failed: {result.stderr}")
            return False
        
        print("  ✅ CLI prediction with live data works")
        print("  📊 Prediction output preview:")
        
        # Show first few lines of output
        output_lines = result.stdout.split('\n')[:10]
        for line in output_lines:
            if line.strip():
                print(f"    {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ CLI interface test FAILED: {e}")
        return False

def test_market_hours_awareness():
    """Test system behavior during market hours vs after hours"""
    print("\n🕐 Testing market hours awareness...")
    
    try:
        import yfinance as yf
        from datetime import datetime
        import pytz
        
        # Get current time in Eastern timezone (market timezone)
        eastern = pytz.timezone('US/Eastern')
        current_time = datetime.now(eastern)
        
        # Check if market is open (rough approximation)
        is_weekday = current_time.weekday() < 5
        is_market_hours = 9 <= current_time.hour < 16
        
        print(f"  🕐 Current time (ET): {current_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        print(f"  📅 Is weekday: {is_weekday}")
        print(f"  🏢 Likely market hours: {is_market_hours}")
        
        # Test data freshness
        ticker = yf.Ticker('AAPL')
        hist = ticker.history(period="1d", interval="1m")
        
        if not hist.empty:
            latest_data_time = hist.index[-1]
            print(f"  📊 Latest data timestamp: {latest_data_time}")
            
            # Check how recent the data is
            time_diff = datetime.now(pytz.UTC) - latest_data_time.tz_convert(pytz.UTC)
            minutes_old = time_diff.total_seconds() / 60
            
            print(f"  ⏰ Data is {minutes_old:.1f} minutes old")
            
            if minutes_old < 60:
                print("  ✅ Data is very fresh")
            elif minutes_old < 240:
                print("  ✅ Data is reasonably fresh")
            else:
                print("  ⚠️ Data may be stale (market closed?)")
        
        return True
        
    except Exception as e:
        print(f"❌ Market hours test FAILED: {e}")
        return False

def run_comprehensive_live_test():
    """Run comprehensive test with live data"""
    print("🚀 StockTrek LIVE System Test")
    print("=" * 50)
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    tests = [
        ("Live Data Fetch", test_live_data_fetch),
        ("Neural Network with Live Data", test_neural_network_with_live_data),
        ("ETL with Live Data", test_etl_with_live_data),
        ("Prediction Accuracy", test_prediction_accuracy),
        ("CLI Interface", test_cli_interface),
        ("Market Hours Awareness", test_market_hours_awareness),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 LIVE Test Results: {passed}/{total} tests passed")
    print(f"🕐 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed == total:
        print("🎉 ALL LIVE TESTS PASSED! StockTrek is 100% ready with live data!")
        print("\n🚀 System is production-ready with up-to-date market data!")
    else:
        print("⚠️ Some live tests failed. Please check the issues above.")
    
    return passed == total

def main():
    """Main test function"""
    success = run_comprehensive_live_test()
    
    if success:
        print("\n🎯 FINAL STATUS: 100% COMPLETE AND TESTED WITH LIVE DATA")
        print("\n📋 Ready for production use:")
        print("  • python main.py --predict AAPL --timeframe 30")
        print("  • python main.py --daemon")
        print("  • python main.py --backtest TSLA --days 90")
    else:
        print("\n❌ FINAL STATUS: NEEDS ATTENTION")
        print("Please fix the failing tests before production use.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
