#!/usr/bin/env python3
"""
Quick test to verify StockTrek works with live data
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_functionality():
    """Test basic functionality"""
    print("🧪 Testing basic StockTrek functionality...")
    
    try:
        # Test yfinance import and live data
        print("📊 Testing yfinance and live data...")
        import yfinance as yf
        
        ticker = yf.Ticker("AAPL")
        hist = ticker.history(period="5d")
        info = ticker.info
        
        if hist.empty:
            print("❌ No data received")
            return False
        
        current_price = hist['Close'].iloc[-1]
        company_name = info.get('longName', 'N/A')
        
        print(f"✅ Live data test passed")
        print(f"   Company: {company_name}")
        print(f"   Current Price: ${current_price:.2f}")
        print(f"   Data points: {len(hist)}")
        
        # Test neural network import
        print("🧠 Testing neural network import...")
        from src.neural_network import NeuralNetworkPredictor
        
        predictor = NeuralNetworkPredictor()
        print("✅ Neural network imported successfully")
        
        # Test feature extraction
        print("🔧 Testing feature extraction...")
        features = predictor.extract_comprehensive_features("AAPL", 30)
        
        if features:
            print(f"✅ Feature extraction successful ({len(features)} features)")
            
            # Show some key features
            key_features = ['current_price', 'rsi', 'volatility']
            for feature in key_features:
                if feature in features:
                    print(f"   {feature}: {features[feature]:.3f}")
        else:
            print("❌ Feature extraction failed")
            return False
        
        # Test prediction
        print("🔮 Testing prediction...")
        prediction = predictor.predict_stock_movement("AAPL", 30)
        
        if 'error' not in prediction:
            print("✅ Prediction successful")
            print(f"   Prediction: {prediction.get('prediction', 'N/A')}")
            print(f"   Confidence: {prediction.get('confidence', 0):.1f}%")
            
            price_targets = prediction.get('price_targets', {})
            if price_targets:
                print(f"   Target Price: ${price_targets.get('target_price', 0):.2f}")
        else:
            print(f"❌ Prediction failed: {prediction['error']}")
            return False
        
        print("\n🎉 All basic tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please install dependencies: pip3 install yfinance pandas numpy scikit-learn")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cli():
    """Test CLI functionality"""
    print("\n💻 Testing CLI...")
    
    try:
        import subprocess
        
        # Test help
        result = subprocess.run([sys.executable, "main.py", "--help"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ CLI help works")
        else:
            print("❌ CLI help failed")
            return False
        
        # Test prediction
        print("🔮 Testing CLI prediction...")
        result = subprocess.run([sys.executable, "main.py", "--predict", "AAPL", "--timeframe", "30"], 
                              capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ CLI prediction works")
            # Show first few lines
            lines = result.stdout.split('\n')[:5]
            for line in lines:
                if line.strip():
                    print(f"   {line}")
        else:
            print("❌ CLI prediction failed")
            print(f"Error: {result.stderr}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ CLI test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 StockTrek Quick Test")
    print("=" * 40)
    
    success = True
    
    if not test_basic_functionality():
        success = False
    
    if not test_cli():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("StockTrek is working with live data!")
        print("\n📋 Ready to use:")
        print("  python3 main.py --predict AAPL --timeframe 30")
        print("  python3 main.py --daemon")
    else:
        print("❌ Some tests failed")
        print("Please install dependencies and try again")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
