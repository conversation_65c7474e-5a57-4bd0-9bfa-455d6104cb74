CREATE TABLE companies (
  id SERIAL PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  ticker VARCHAR(10) NOT NULL
);

CREATE TABLE company_data (
  company_id INTEGER NOT NULL REFERENCES companies(id),
  -- Store trailing 5 years as JSONB (e.g., {"2020": 1000, "2021": 1100, ...}) or just {"TTM": 1200}
  revenue JSONB,
  revenue_growth_rate_fwd NUMERIC,
  revenue_growth_rate_trailing JSONB, -- optional, if you want to store trailing growth rates
  ebitda JSONB,
  ebitda_growth_rate_fwd NUMERIC,
  ebitda_growth_rate_trailing JSONB,
  depreciation_amortization JSONB,
  ebit JSONB,
  capex JSONB,
  working_capital JSONB,
  tax_rate NUMERIC,
  levered_fcf JSONB,
  wacc NUMERIC,
  debt_to_equity_ratio NUMERIC,
  current_ratio NUMERIC,
  quick_ratio NUMERIC,
  gross_profit_margin NUMERIC,
  pe_ratio NUMERIC,
  eps NUMERIC,
  ps_ratio NUMERIC,
  dividend_yield_percentage NUMERIC,
  ev_to_ebitda NUMERIC,
  net_income JSONB,
  sentiment_data TEXT NOT NULL,
  as_of_date DATE NOT NULL DEFAULT CURRENT_DATE
);