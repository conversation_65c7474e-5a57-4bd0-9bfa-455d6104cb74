2025-06-30 20:07:45,733 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:07:51,409 - src.data_manager - INFO - ✅ Database connection successful
2025-06-30 20:07:51,409 - src.neural_network - WARNING - ⚠️ Neural network model files not found, will need training
2025-06-30 20:07:51,410 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:07:51,410 - __main__ - INFO - 🔮 Making prediction for AAPL with 30 day timeframe
2025-06-30 20:07:51,410 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 20:07:56,711 - src.neural_network - INFO - 🔮 Starting neural network prediction for AAPL
2025-06-30 20:07:57,198 - src.neural_network - WARNING - ⚠️ Neural network model not available, using fallback
2025-06-30 20:07:57,232 - src.data_manager - ERROR - ❌ Database error: relation "predictions" does not exist
LINE 2:                     INSERT INTO predictions (
                                        ^

2025-06-30 20:07:57,232 - src.data_manager - ERROR - ❌ Error storing prediction: relation "predictions" does not exist
LINE 2:                     INSERT INTO predictions (
                                        ^

2025-06-30 20:08:22,058 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:08:22,126 - src.data_manager - INFO - ✅ Database connection successful
2025-06-30 20:08:22,126 - src.neural_network - WARNING - ⚠️ Neural network model files not found, will need training
2025-06-30 20:08:22,126 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:08:22,126 - __main__ - INFO - 🔮 Making prediction for DE with 1 day timeframe
2025-06-30 20:08:22,127 - __main__ - INFO - 📊 Updating data for DE...
2025-06-30 20:08:26,177 - src.neural_network - INFO - 🔮 Starting neural network prediction for DE
2025-06-30 20:08:26,684 - src.neural_network - WARNING - ⚠️ Neural network model not available, using fallback
2025-06-30 20:08:26,723 - src.data_manager - ERROR - ❌ Database error: relation "predictions" does not exist
LINE 2:                     INSERT INTO predictions (
                                        ^

2025-06-30 20:08:26,723 - src.data_manager - ERROR - ❌ Error storing prediction: relation "predictions" does not exist
LINE 2:                     INSERT INTO predictions (
                                        ^

2025-06-30 20:08:41,258 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:08:41,319 - src.data_manager - INFO - ✅ Database connection successful
2025-06-30 20:08:41,319 - src.neural_network - WARNING - ⚠️ Neural network model files not found, will need training
2025-06-30 20:08:41,320 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:08:41,320 - __main__ - INFO - 🔮 Making prediction for AAPL with 1 day timeframe
2025-06-30 20:08:41,321 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 20:08:45,775 - src.neural_network - INFO - 🔮 Starting neural network prediction for AAPL
2025-06-30 20:08:46,280 - src.neural_network - WARNING - ⚠️ Neural network model not available, using fallback
2025-06-30 20:08:46,319 - src.data_manager - ERROR - ❌ Database error: relation "predictions" does not exist
LINE 2:                     INSERT INTO predictions (
                                        ^

2025-06-30 20:08:46,319 - src.data_manager - ERROR - ❌ Error storing prediction: relation "predictions" does not exist
LINE 2:                     INSERT INTO predictions (
                                        ^

2025-06-30 20:22:58,928 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:22:59,082 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 20:22:59,136 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 20:22:59,136 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 20:22:59,137 - src.neural_network - WARNING - ⚠️ Neural network model files not found, will need training
2025-06-30 20:22:59,139 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:22:59,139 - __main__ - INFO - 🔮 Making prediction for AAPL with 30 day timeframe
2025-06-30 20:22:59,139 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 20:23:03,504 - src.neural_network - INFO - 🔮 Starting neural network prediction for AAPL
2025-06-30 20:23:03,994 - src.neural_network - WARNING - ⚠️ Neural network model not available, using fallback
2025-06-30 20:23:04,031 - src.data_manager - INFO - ✅ Stored prediction 2 for AAPL
2025-06-30 20:26:11,307 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:26:11,517 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 20:26:11,590 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 20:26:11,590 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 20:26:11,591 - src.neural_network - WARNING - ⚠️ Neural network model files not found, will need training
2025-06-30 20:26:11,591 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:26:11,591 - __main__ - INFO - 🔮 Making prediction for AAPL with 30 day timeframe
2025-06-30 20:26:11,591 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 20:26:15,923 - src.neural_network - INFO - 🔮 Starting neural network prediction for AAPL
2025-06-30 20:26:16,873 - src.neural_network - WARNING - ⚠️ Neural network model not available, using fallback
2025-06-30 20:26:16,990 - src.data_manager - INFO - ✅ Stored prediction 3 for AAPL
2025-06-30 20:45:32,439 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:45:32,592 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 20:45:32,654 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 20:45:32,654 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 20:45:32,655 - src.neural_network - WARNING - ⚠️ Neural network model files not found, will need training
2025-06-30 20:45:32,655 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:45:32,656 - __main__ - INFO - 🔮 Making prediction for AAPL with 30 day timeframe
2025-06-30 20:45:32,656 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 20:45:37,309 - src.neural_network - INFO - 🔮 Starting neural network prediction for AAPL
2025-06-30 20:45:37,949 - src.neural_network - WARNING - ⚠️ Neural network model not available, using fallback
2025-06-30 20:45:37,981 - src.data_manager - INFO - ✅ Stored prediction 4 for AAPL
2025-06-30 20:45:56,537 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:45:56,672 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 20:45:56,692 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 20:45:56,692 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 20:45:56,693 - src.neural_network - WARNING - ⚠️ Neural network model files not found, will need training
2025-06-30 20:45:56,693 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:45:56,693 - __main__ - INFO - 🔮 Making prediction for NVDA with 30 day timeframe
2025-06-30 20:45:56,693 - __main__ - INFO - 📊 Updating data for NVDA...
2025-06-30 20:46:00,828 - src.neural_network - INFO - 🔮 Starting neural network prediction for NVDA
2025-06-30 20:46:01,303 - src.neural_network - WARNING - ⚠️ Neural network model not available, using fallback
2025-06-30 20:46:01,340 - src.data_manager - INFO - ✅ Stored prediction 5 for NVDA
2025-06-30 20:53:00,829 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:53:00,993 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 20:53:01,047 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 20:53:01,048 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 20:53:01,266 - src.neural_network - INFO - ✅ Neural network model loaded successfully
2025-06-30 20:53:01,266 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:53:01,266 - __main__ - INFO - 🔮 Making prediction for AAPL with 30 day timeframe
2025-06-30 20:53:01,266 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 20:53:05,577 - src.neural_network - INFO - 🔮 Starting neural network prediction for AAPL
2025-06-30 20:53:06,191 - src.neural_network - INFO - ✅ Neural network prediction completed for AAPL: BUY (62.8%)
2025-06-30 20:53:06,247 - src.data_manager - ERROR - ❌ Database error: schema "np" does not exist
LINE 7: ... 30, 'BUY', 62.79283942333712, 205.1699981689453, np.float64...
                                                             ^

2025-06-30 20:53:06,247 - src.data_manager - ERROR - ❌ Error storing prediction: schema "np" does not exist
LINE 7: ... 30, 'BUY', 62.79283942333712, 205.1699981689453, np.float64...
                                                             ^

2025-06-30 20:53:38,914 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:53:39,088 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 20:53:39,111 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 20:53:39,111 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 20:53:39,366 - src.neural_network - INFO - ✅ Neural network model loaded successfully
2025-06-30 20:53:39,366 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:53:39,367 - __main__ - INFO - 🔮 Making prediction for VOYG with 30 day timeframe
2025-06-30 20:53:39,367 - __main__ - INFO - 📊 Updating data for VOYG...
2025-06-30 20:53:41,410 - yfinance - ERROR - HTTP Error 404: 
2025-06-30 20:53:44,029 - __main__ - ERROR - ❌ Error making prediction for VOYG: null value in column "symbol" of relation "companies" violates not-null constraint
DETAIL:  Failing row contains (22, Voyager Technologies, Inc., VOYG, null, 2025-06-30 20:53:43.982851, 2025-06-30 20:53:43.982851).

