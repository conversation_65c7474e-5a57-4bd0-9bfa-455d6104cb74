2025-06-30 20:07:45,733 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:07:51,409 - src.data_manager - INFO - ✅ Database connection successful
2025-06-30 20:07:51,409 - src.neural_network - WARNING - ⚠️ Neural network model files not found, will need training
2025-06-30 20:07:51,410 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:07:51,410 - __main__ - INFO - 🔮 Making prediction for AAPL with 30 day timeframe
2025-06-30 20:07:51,410 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 20:07:56,711 - src.neural_network - INFO - 🔮 Starting neural network prediction for AAPL
2025-06-30 20:07:57,198 - src.neural_network - WARNING - ⚠️ Neural network model not available, using fallback
2025-06-30 20:07:57,232 - src.data_manager - ERROR - ❌ Database error: relation "predictions" does not exist
LINE 2:                     INSERT INTO predictions (
                                        ^

2025-06-30 20:07:57,232 - src.data_manager - ERROR - ❌ Error storing prediction: relation "predictions" does not exist
LINE 2:                     INSERT INTO predictions (
                                        ^

2025-06-30 20:08:22,058 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:08:22,126 - src.data_manager - INFO - ✅ Database connection successful
2025-06-30 20:08:22,126 - src.neural_network - WARNING - ⚠️ Neural network model files not found, will need training
2025-06-30 20:08:22,126 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:08:22,126 - __main__ - INFO - 🔮 Making prediction for DE with 1 day timeframe
2025-06-30 20:08:22,127 - __main__ - INFO - 📊 Updating data for DE...
2025-06-30 20:08:26,177 - src.neural_network - INFO - 🔮 Starting neural network prediction for DE
2025-06-30 20:08:26,684 - src.neural_network - WARNING - ⚠️ Neural network model not available, using fallback
2025-06-30 20:08:26,723 - src.data_manager - ERROR - ❌ Database error: relation "predictions" does not exist
LINE 2:                     INSERT INTO predictions (
                                        ^

2025-06-30 20:08:26,723 - src.data_manager - ERROR - ❌ Error storing prediction: relation "predictions" does not exist
LINE 2:                     INSERT INTO predictions (
                                        ^

2025-06-30 20:08:41,258 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:08:41,319 - src.data_manager - INFO - ✅ Database connection successful
2025-06-30 20:08:41,319 - src.neural_network - WARNING - ⚠️ Neural network model files not found, will need training
2025-06-30 20:08:41,320 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:08:41,320 - __main__ - INFO - 🔮 Making prediction for AAPL with 1 day timeframe
2025-06-30 20:08:41,321 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 20:08:45,775 - src.neural_network - INFO - 🔮 Starting neural network prediction for AAPL
2025-06-30 20:08:46,280 - src.neural_network - WARNING - ⚠️ Neural network model not available, using fallback
2025-06-30 20:08:46,319 - src.data_manager - ERROR - ❌ Database error: relation "predictions" does not exist
LINE 2:                     INSERT INTO predictions (
                                        ^

2025-06-30 20:08:46,319 - src.data_manager - ERROR - ❌ Error storing prediction: relation "predictions" does not exist
LINE 2:                     INSERT INTO predictions (
                                        ^

2025-06-30 20:22:58,928 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:22:59,082 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 20:22:59,136 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 20:22:59,136 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 20:22:59,137 - src.neural_network - WARNING - ⚠️ Neural network model files not found, will need training
2025-06-30 20:22:59,139 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:22:59,139 - __main__ - INFO - 🔮 Making prediction for AAPL with 30 day timeframe
2025-06-30 20:22:59,139 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 20:23:03,504 - src.neural_network - INFO - 🔮 Starting neural network prediction for AAPL
2025-06-30 20:23:03,994 - src.neural_network - WARNING - ⚠️ Neural network model not available, using fallback
2025-06-30 20:23:04,031 - src.data_manager - INFO - ✅ Stored prediction 2 for AAPL
2025-06-30 20:26:11,307 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:26:11,517 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 20:26:11,590 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 20:26:11,590 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 20:26:11,591 - src.neural_network - WARNING - ⚠️ Neural network model files not found, will need training
2025-06-30 20:26:11,591 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:26:11,591 - __main__ - INFO - 🔮 Making prediction for AAPL with 30 day timeframe
2025-06-30 20:26:11,591 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 20:26:15,923 - src.neural_network - INFO - 🔮 Starting neural network prediction for AAPL
2025-06-30 20:26:16,873 - src.neural_network - WARNING - ⚠️ Neural network model not available, using fallback
2025-06-30 20:26:16,990 - src.data_manager - INFO - ✅ Stored prediction 3 for AAPL
2025-06-30 20:45:32,439 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:45:32,592 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 20:45:32,654 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 20:45:32,654 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 20:45:32,655 - src.neural_network - WARNING - ⚠️ Neural network model files not found, will need training
2025-06-30 20:45:32,655 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:45:32,656 - __main__ - INFO - 🔮 Making prediction for AAPL with 30 day timeframe
2025-06-30 20:45:32,656 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 20:45:37,309 - src.neural_network - INFO - 🔮 Starting neural network prediction for AAPL
2025-06-30 20:45:37,949 - src.neural_network - WARNING - ⚠️ Neural network model not available, using fallback
2025-06-30 20:45:37,981 - src.data_manager - INFO - ✅ Stored prediction 4 for AAPL
2025-06-30 20:45:56,537 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:45:56,672 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 20:45:56,692 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 20:45:56,692 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 20:45:56,693 - src.neural_network - WARNING - ⚠️ Neural network model files not found, will need training
2025-06-30 20:45:56,693 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:45:56,693 - __main__ - INFO - 🔮 Making prediction for NVDA with 30 day timeframe
2025-06-30 20:45:56,693 - __main__ - INFO - 📊 Updating data for NVDA...
2025-06-30 20:46:00,828 - src.neural_network - INFO - 🔮 Starting neural network prediction for NVDA
2025-06-30 20:46:01,303 - src.neural_network - WARNING - ⚠️ Neural network model not available, using fallback
2025-06-30 20:46:01,340 - src.data_manager - INFO - ✅ Stored prediction 5 for NVDA
2025-06-30 20:53:00,829 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:53:00,993 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 20:53:01,047 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 20:53:01,048 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 20:53:01,266 - src.neural_network - INFO - ✅ Neural network model loaded successfully
2025-06-30 20:53:01,266 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:53:01,266 - __main__ - INFO - 🔮 Making prediction for AAPL with 30 day timeframe
2025-06-30 20:53:01,266 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 20:53:05,577 - src.neural_network - INFO - 🔮 Starting neural network prediction for AAPL
2025-06-30 20:53:06,191 - src.neural_network - INFO - ✅ Neural network prediction completed for AAPL: BUY (62.8%)
2025-06-30 20:53:06,247 - src.data_manager - ERROR - ❌ Database error: schema "np" does not exist
LINE 7: ... 30, 'BUY', 62.79283942333712, 205.1699981689453, np.float64...
                                                             ^

2025-06-30 20:53:06,247 - src.data_manager - ERROR - ❌ Error storing prediction: schema "np" does not exist
LINE 7: ... 30, 'BUY', 62.79283942333712, 205.1699981689453, np.float64...
                                                             ^

2025-06-30 20:53:38,914 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 20:53:39,088 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 20:53:39,111 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 20:53:39,111 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 20:53:39,366 - src.neural_network - INFO - ✅ Neural network model loaded successfully
2025-06-30 20:53:39,366 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 20:53:39,367 - __main__ - INFO - 🔮 Making prediction for VOYG with 30 day timeframe
2025-06-30 20:53:39,367 - __main__ - INFO - 📊 Updating data for VOYG...
2025-06-30 20:53:41,410 - yfinance - ERROR - HTTP Error 404: 
2025-06-30 20:53:44,029 - __main__ - ERROR - ❌ Error making prediction for VOYG: null value in column "symbol" of relation "companies" violates not-null constraint
DETAIL:  Failing row contains (22, Voyager Technologies, Inc., VOYG, null, 2025-06-30 20:53:43.982851, 2025-06-30 20:53:43.982851).

2025-06-30 21:06:05,114 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:06:05,367 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:06:05,434 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:06:05,434 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:06:05,435 - src.autonomous_neural_network - INFO - 📚 No existing model found, will train new model
2025-06-30 21:06:05,435 - src.neural_network - INFO - 🚀 Autonomous Neural Network System initialized
2025-06-30 21:06:05,435 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:06:05,436 - __main__ - INFO - 🔮 Making prediction for AAPL with 30 day timeframe
2025-06-30 21:06:05,436 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 21:06:10,892 - __main__ - ERROR - ❌ Error making prediction for AAPL: 'NeuralNetworkPredictor' object has no attribute 'predict_stock_movement'
2025-06-30 21:17:04,223 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:17:04,443 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:17:04,543 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:17:04,543 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:17:04,545 - src.autonomous_neural_network - INFO - 📊 Loaded 147 cached stocks
2025-06-30 21:17:04,547 - src.autonomous_neural_network - INFO - 📚 No existing model found, will train new model
2025-06-30 21:17:04,547 - src.neural_network - INFO - 🚀 Autonomous Neural Network System initialized
2025-06-30 21:17:04,547 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:17:04,548 - __main__ - INFO - 🔮 Making prediction for AAPL with 5 day timeframe
2025-06-30 21:17:04,548 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 21:17:08,435 - __main__ - ERROR - ❌ Error making prediction for AAPL: 'NeuralNetworkPredictor' object has no attribute 'predict_stock_movement'
2025-06-30 21:22:32,948 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:22:33,105 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:22:33,166 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:22:33,168 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:22:33,169 - src.neural_network - INFO - 🚀 Initializing Self-Learning Neural Network API
2025-06-30 21:22:33,170 - src.neural_network - INFO - 📚 No existing model found - starting fresh
2025-06-30 21:22:33,171 - src.neural_network - INFO - 📊 Ready! Training samples: 0
2025-06-30 21:22:33,171 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:22:33,171 - __main__ - INFO - 🔮 Making prediction for AAPL with 30 day timeframe
2025-06-30 21:22:33,172 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 21:22:38,182 - src.neural_network - INFO - 🔮 Starting neural network prediction for AAPL
2025-06-30 21:22:38,654 - src.neural_network - INFO - 📊 Technical analysis prediction for AAPL: HOLD (53.0%)
2025-06-30 21:22:38,709 - src.data_manager - INFO - ✅ Stored prediction 6 for AAPL
2025-06-30 21:22:52,171 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:22:52,225 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:22:52,235 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:22:52,236 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:22:52,236 - src.neural_network - INFO - 🚀 Initializing Self-Learning Neural Network API
2025-06-30 21:22:52,237 - src.neural_network - INFO - 📚 No existing model found - starting fresh
2025-06-30 21:22:52,237 - src.neural_network - INFO - 📊 Ready! Training samples: 0
2025-06-30 21:22:52,238 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:22:52,238 - __main__ - INFO - 🔮 Making prediction for NVDA with 30 day timeframe
2025-06-30 21:22:52,239 - __main__ - INFO - 📊 Updating data for NVDA...
2025-06-30 21:22:56,407 - src.neural_network - INFO - 🔮 Starting neural network prediction for NVDA
2025-06-30 21:22:56,646 - src.neural_network - INFO - 📊 Technical analysis prediction for NVDA: HOLD (56.0%)
2025-06-30 21:22:56,717 - src.data_manager - INFO - ✅ Stored prediction 7 for NVDA
2025-06-30 21:23:06,763 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:23:06,842 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:23:06,858 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:23:06,860 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:23:06,863 - src.neural_network - INFO - 🚀 Initializing Self-Learning Neural Network API
2025-06-30 21:23:06,865 - src.neural_network - INFO - 📚 No existing model found - starting fresh
2025-06-30 21:23:06,865 - src.neural_network - INFO - 📊 Ready! Training samples: 0
2025-06-30 21:23:06,865 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:23:06,865 - __main__ - INFO - 🔮 Making prediction for TSLA with 7 day timeframe
2025-06-30 21:23:06,865 - __main__ - INFO - 📊 Updating data for TSLA...
2025-06-30 21:23:10,845 - src.neural_network - INFO - 🔮 Starting neural network prediction for TSLA
2025-06-30 21:23:11,030 - src.neural_network - INFO - 📊 Technical analysis prediction for TSLA: HOLD (55.0%)
2025-06-30 21:23:11,093 - src.data_manager - INFO - ✅ Stored prediction 8 for TSLA
2025-06-30 21:29:30,118 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:29:30,293 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:29:30,312 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:29:30,313 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:29:30,313 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:29:30,313 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:29:30,313 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:29:30,314 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:29:30,314 - __main__ - INFO - 🔮 Making prediction for AAPL with 30 day timeframe
2025-06-30 21:29:30,314 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 21:29:35,472 - src.neural_network - INFO - 🔮 Neural Network Prediction #1 for AAPL
2025-06-30 21:29:36,144 - src.neural_network - INFO - ✅ Prediction: HOLD (61.0%) | Model: technical_analysis_enhanced
2025-06-30 21:29:36,145 - __main__ - ERROR - ❌ Error making prediction for AAPL: 'NeuralNetworkPredictor' object has no attribute 'prediction_history'
2025-06-30 21:30:07,002 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:30:07,141 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:30:07,163 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:30:07,163 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:30:07,164 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:30:07,165 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:30:07,165 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:30:07,165 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:30:07,165 - __main__ - INFO - 🔮 Making prediction for AAPL with 30 day timeframe
2025-06-30 21:30:07,165 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 21:30:11,574 - src.neural_network - INFO - 🔮 Neural Network Prediction #1 for AAPL
2025-06-30 21:30:12,131 - src.neural_network - INFO - ✅ Prediction: HOLD (61.0%) | Model: technical_analysis_enhanced
2025-06-30 21:30:12,170 - src.data_manager - INFO - ✅ Stored prediction 9 for AAPL
2025-06-30 21:31:10,216 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:31:10,382 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:31:10,405 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:31:10,405 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:31:10,405 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:31:10,405 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:31:10,406 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:31:10,406 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:31:10,406 - __main__ - INFO - 🔮 Making prediction for VOYG with 30 day timeframe
2025-06-30 21:31:10,406 - __main__ - INFO - 📊 Updating data for VOYG...
2025-06-30 21:31:12,524 - yfinance - ERROR - HTTP Error 404: 
2025-06-30 21:31:16,472 - __main__ - ERROR - ❌ Error making prediction for VOYG: null value in column "symbol" of relation "companies" violates not-null constraint
DETAIL:  Failing row contains (23, Voyager Technologies, Inc., VOYG, null, 2025-06-30 21:31:16.373058, 2025-06-30 21:31:16.373058).

2025-06-30 21:31:24,648 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:31:24,756 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:31:24,815 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:31:24,815 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:31:24,816 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:31:24,816 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:31:24,816 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:31:24,817 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:31:24,817 - __main__ - INFO - 🔮 Making prediction for TSLA with 7 day timeframe
2025-06-30 21:31:24,817 - __main__ - INFO - 📊 Updating data for TSLA...
2025-06-30 21:31:30,891 - src.neural_network - INFO - 🔮 Neural Network Prediction #1 for TSLA
2025-06-30 21:31:31,335 - src.neural_network - INFO - ✅ Prediction: SELL (71.5%) | Model: technical_analysis_enhanced
2025-06-30 21:31:31,415 - src.data_manager - INFO - ✅ Stored prediction 10 for TSLA
2025-06-30 21:31:45,100 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:31:45,181 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:31:45,215 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:31:45,216 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:31:45,216 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:31:45,217 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:31:45,217 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:31:45,217 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:31:45,217 - __main__ - INFO - 🔮 Making prediction for MSFT with 14 day timeframe
2025-06-30 21:31:45,218 - __main__ - INFO - 📊 Updating data for MSFT...
2025-06-30 21:31:49,393 - src.neural_network - INFO - 🔮 Neural Network Prediction #1 for MSFT
2025-06-30 21:31:50,185 - src.neural_network - INFO - ✅ Prediction: SELL (81.0%) | Model: technical_analysis_enhanced
2025-06-30 21:31:50,493 - src.data_manager - INFO - ✅ Stored prediction 11 for MSFT
2025-06-30 21:32:08,520 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:32:09,052 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:32:09,084 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:32:09,085 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:32:09,085 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:32:09,085 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:32:09,086 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:32:09,086 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:32:09,086 - __main__ - INFO - 🔮 Making prediction for GOOGL with 21 day timeframe
2025-06-30 21:32:09,086 - __main__ - INFO - 📊 Updating data for GOOGL...
2025-06-30 21:32:13,294 - src.neural_network - INFO - 🔮 Neural Network Prediction #1 for GOOGL
2025-06-30 21:32:13,771 - src.neural_network - INFO - ✅ Prediction: HOLD (55.0%) | Model: technical_analysis_enhanced
2025-06-30 21:32:13,912 - src.data_manager - INFO - ✅ Stored prediction 12 for GOOGL
2025-06-30 21:32:18,186 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:32:18,247 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:32:18,302 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:32:18,304 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:32:18,305 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:32:18,306 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:32:18,311 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:32:18,311 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:32:18,311 - __main__ - INFO - 🔮 Making prediction for AMZN with 21 day timeframe
2025-06-30 21:32:18,312 - __main__ - INFO - 📊 Updating data for AMZN...
2025-06-30 21:32:19,347 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:32:19,406 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:32:19,421 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:32:19,421 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:32:19,421 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:32:19,422 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:32:19,422 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:32:19,422 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:32:19,422 - __main__ - INFO - 🔮 Making prediction for HON with 30 day timeframe
2025-06-30 21:32:19,422 - __main__ - INFO - 📊 Updating data for HON...
2025-06-30 21:32:22,589 - __main__ - ERROR - ❌ Error making prediction for AMZN: null value in column "symbol" of relation "companies" violates not-null constraint
DETAIL:  Failing row contains (24, Amazon.com, Inc., AMZN, null, 2025-06-30 21:32:22.584859, 2025-06-30 21:32:22.584859).

2025-06-30 21:32:23,473 - __main__ - ERROR - ❌ Error making prediction for HON: null value in column "symbol" of relation "companies" violates not-null constraint
DETAIL:  Failing row contains (25, Honeywell International Inc., HON, null, 2025-06-30 21:32:23.469152, 2025-06-30 21:32:23.469152).

2025-06-30 21:32:26,100 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:32:26,152 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:32:26,166 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:32:26,168 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:32:26,168 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:32:26,169 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:32:26,169 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:32:26,169 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:32:26,169 - __main__ - INFO - 🔮 Making prediction for NVDA with 21 day timeframe
2025-06-30 21:32:26,170 - __main__ - INFO - 📊 Updating data for NVDA...
2025-06-30 21:32:30,188 - src.neural_network - INFO - 🔮 Neural Network Prediction #1 for NVDA
2025-06-30 21:32:30,902 - src.neural_network - INFO - ✅ Prediction: HOLD (61.0%) | Model: technical_analysis_enhanced
2025-06-30 21:32:30,985 - src.data_manager - INFO - ✅ Stored prediction 13 for NVDA
2025-06-30 21:32:34,869 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:32:34,937 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:32:34,951 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:32:34,952 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:32:34,952 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:32:34,952 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:32:34,953 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:32:34,953 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:32:34,953 - __main__ - INFO - 🔮 Making prediction for META with 21 day timeframe
2025-06-30 21:32:34,953 - __main__ - INFO - 📊 Updating data for META...
2025-06-30 21:32:39,229 - __main__ - ERROR - ❌ Error making prediction for META: null value in column "symbol" of relation "companies" violates not-null constraint
DETAIL:  Failing row contains (26, Meta Platforms, Inc., META, null, 2025-06-30 21:32:39.223351, 2025-06-30 21:32:39.223351).

2025-06-30 21:34:43,971 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:34:44,200 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:34:44,274 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:34:44,275 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:34:44,275 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:34:44,276 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:34:44,276 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:34:44,276 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:34:44,276 - __main__ - INFO - 🔮 Making prediction for AAPL with 30 day timeframe
2025-06-30 21:34:44,276 - __main__ - INFO - 📊 Updating data for AAPL...
2025-06-30 21:34:48,856 - src.neural_network - INFO - 🔮 Neural Network Prediction #1 for AAPL
2025-06-30 21:34:49,410 - src.neural_network - INFO - ✅ Prediction: HOLD (61.0%) | Model: technical_analysis_enhanced
2025-06-30 21:34:49,503 - src.data_manager - INFO - ✅ Stored prediction 14 for AAPL
2025-06-30 21:35:01,472 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:35:01,550 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:35:01,568 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:35:01,569 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:35:01,569 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:35:01,570 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:35:01,570 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:35:01,570 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:35:01,570 - __main__ - INFO - 🔮 Making prediction for TSLA with 7 day timeframe
2025-06-30 21:35:01,570 - __main__ - INFO - 📊 Updating data for TSLA...
2025-06-30 21:35:05,800 - src.neural_network - INFO - 🔮 Neural Network Prediction #1 for TSLA
2025-06-30 21:35:06,246 - src.neural_network - INFO - ✅ Prediction: SELL (71.5%) | Model: technical_analysis_enhanced
2025-06-30 21:35:06,311 - src.data_manager - INFO - ✅ Stored prediction 15 for TSLA
2025-06-30 21:35:37,493 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:35:37,636 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:35:37,652 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:35:37,652 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:35:37,652 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:35:37,653 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:35:37,653 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:35:37,653 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:35:37,653 - __main__ - INFO - 🔮 Making prediction for MSFT with 14 day timeframe
2025-06-30 21:35:37,654 - __main__ - INFO - 📊 Updating data for MSFT...
2025-06-30 21:35:41,960 - src.neural_network - INFO - 🔮 Neural Network Prediction #1 for MSFT
2025-06-30 21:35:42,352 - src.neural_network - INFO - ✅ Prediction: SELL (81.0%) | Model: technical_analysis_enhanced
2025-06-30 21:35:42,390 - src.data_manager - INFO - ✅ Stored prediction 16 for MSFT
2025-06-30 21:35:46,919 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:35:46,969 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:35:46,982 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:35:46,984 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:35:46,984 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:35:46,985 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:35:46,985 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:35:46,985 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:35:46,986 - __main__ - INFO - 🔮 Making prediction for GOOGL with 21 day timeframe
2025-06-30 21:35:46,986 - __main__ - INFO - 📊 Updating data for GOOGL...
2025-06-30 21:35:51,256 - src.neural_network - INFO - 🔮 Neural Network Prediction #1 for GOOGL
2025-06-30 21:35:51,684 - src.neural_network - INFO - ✅ Prediction: HOLD (55.0%) | Model: technical_analysis_enhanced
2025-06-30 21:35:51,716 - src.data_manager - INFO - ✅ Stored prediction 17 for GOOGL
2025-06-30 21:35:54,695 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:35:54,732 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:35:54,754 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:35:54,755 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:35:54,755 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:35:54,756 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:35:54,756 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:35:54,756 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:35:54,756 - __main__ - INFO - 🔮 Making prediction for AMZN with 30 day timeframe
2025-06-30 21:35:54,756 - __main__ - INFO - 📊 Updating data for AMZN...
2025-06-30 21:35:58,566 - src.neural_network - INFO - 🔮 Neural Network Prediction #1 for AMZN
2025-06-30 21:35:59,084 - src.neural_network - INFO - ✅ Prediction: HOLD (55.0%) | Model: technical_analysis_enhanced
2025-06-30 21:35:59,131 - src.data_manager - INFO - ✅ Stored prediction 18 for AMZN
2025-06-30 21:36:02,528 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:36:02,582 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:36:02,593 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:36:02,593 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:36:02,593 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:36:02,594 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:36:02,594 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:36:02,594 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:36:02,594 - __main__ - INFO - 🔮 Making prediction for NVDA with 7 day timeframe
2025-06-30 21:36:02,594 - __main__ - INFO - 📊 Updating data for NVDA...
2025-06-30 21:36:06,237 - src.neural_network - INFO - 🔮 Neural Network Prediction #1 for NVDA
2025-06-30 21:36:06,657 - src.neural_network - INFO - ✅ Prediction: HOLD (67.1%) | Model: technical_analysis_enhanced
2025-06-30 21:36:06,701 - src.data_manager - INFO - ✅ Stored prediction 19 for NVDA
2025-06-30 21:36:10,234 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:36:10,280 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:36:10,292 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:36:10,292 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:36:10,292 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:36:10,292 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:36:10,293 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:36:10,293 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:36:10,293 - __main__ - INFO - 🔮 Making prediction for META with 14 day timeframe
2025-06-30 21:36:10,293 - __main__ - INFO - 📊 Updating data for META...
2025-06-30 21:36:14,432 - src.neural_network - INFO - 🔮 Neural Network Prediction #1 for META
2025-06-30 21:36:14,906 - src.neural_network - INFO - ✅ Prediction: SELL (71.0%) | Model: technical_analysis_enhanced
2025-06-30 21:36:14,968 - src.data_manager - INFO - ✅ Stored prediction 20 for META
2025-06-30 21:36:19,171 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:36:19,223 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:36:19,238 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:36:19,238 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:36:19,238 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:36:19,239 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:36:19,239 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:36:19,239 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:36:19,239 - __main__ - INFO - 🔮 Making prediction for NFLX with 21 day timeframe
2025-06-30 21:36:19,239 - __main__ - INFO - 📊 Updating data for NFLX...
2025-06-30 21:36:22,869 - src.neural_network - INFO - 🔮 Neural Network Prediction #1 for NFLX
2025-06-30 21:36:23,245 - src.neural_network - INFO - ✅ Prediction: SELL (81.0%) | Model: technical_analysis_enhanced
2025-06-30 21:36:23,284 - src.data_manager - INFO - ✅ Stored prediction 21 for NFLX
2025-06-30 21:36:26,934 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:36:26,976 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:36:26,991 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:36:26,991 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:36:26,991 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:36:26,992 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:36:26,992 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:36:26,992 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:36:26,992 - __main__ - INFO - 🔮 Making prediction for JPM with 30 day timeframe
2025-06-30 21:36:26,992 - __main__ - INFO - 📊 Updating data for JPM...
2025-06-30 21:36:30,839 - src.neural_network - INFO - 🔮 Neural Network Prediction #1 for JPM
2025-06-30 21:36:31,326 - src.neural_network - INFO - ✅ Prediction: SELL (81.0%) | Model: technical_analysis_enhanced
2025-06-30 21:36:31,364 - src.data_manager - INFO - ✅ Stored prediction 22 for JPM
2025-06-30 21:36:34,411 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:36:34,456 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:36:34,467 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:36:34,467 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:36:34,467 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:36:34,468 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:36:34,468 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:36:34,468 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:36:34,468 - __main__ - INFO - 🔮 Making prediction for BAC with 7 day timeframe
2025-06-30 21:36:34,468 - __main__ - INFO - 📊 Updating data for BAC...
2025-06-30 21:36:38,491 - src.neural_network - INFO - 🔮 Neural Network Prediction #1 for BAC
2025-06-30 21:36:39,122 - src.neural_network - INFO - ✅ Prediction: SELL (78.1%) | Model: technical_analysis_enhanced
2025-06-30 21:36:39,163 - src.data_manager - INFO - ✅ Stored prediction 23 for BAC
2025-06-30 21:36:43,092 - __main__ - INFO - Initializing StockTrek components...
2025-06-30 21:36:43,147 - src.data_manager - INFO - 📊 Connected to PostgreSQL: PostgreSQL 17.5 (Postgres.app) on aarch64-apple-darwin23.6.0, compiled by Apple clang version 15.0.0 (clang-1500.3.9.4), 64-bit
2025-06-30 21:36:43,157 - src.data_manager - INFO - ✅ Database schema created successfully
2025-06-30 21:36:43,158 - src.data_manager - INFO - ✅ Database initialized successfully
2025-06-30 21:36:43,158 - src.neural_network - INFO - 🚀 Initializing Production Neural Network System
2025-06-30 21:36:43,158 - src.neural_network - INFO - 📚 Starting fresh - will train with first predictions
2025-06-30 21:36:43,158 - src.neural_network - INFO - ✅ Neural Network Ready - Training samples: 0, Model fitted: False
2025-06-30 21:36:43,158 - __main__ - INFO - ✅ All components initialized successfully
2025-06-30 21:36:43,159 - __main__ - INFO - 🔮 Making prediction for V with 14 day timeframe
2025-06-30 21:36:43,159 - __main__ - INFO - 📊 Updating data for V...
2025-06-30 21:36:44,478 - __main__ - INFO - 🛑 Application interrupted by user
