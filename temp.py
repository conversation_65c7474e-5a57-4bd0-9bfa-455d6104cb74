#!/usr/bin/env python3
"""
temp.py - Simple interactive testing script for financial.py and sentiment.py

Usage: python temp.py
"""

# Import your modules
try:
    from Preprocessing.financial import fetch_data, process_data

    financial_available = True
except ImportError as e:
    print(f"❌ Could not import financial module: {e}")
    financial_available = False

try:
    from Preprocessing.sentiment import SentimentAnalyzer, get_company_info_from_ticker

    sentiment_available = True
except ImportError as e:
    print(f"❌ Could not import sentiment module: {e}")
    sentiment_available = False

from ETL.etl import etl_for_ticker


def format_currency(value):
    """Format large numbers as currency"""
    if value is None:
        return "N/A"

    if abs(value) >= 1e12:
        return f"${value / 1e12:.2f}T"
    elif abs(value) >= 1e9:
        return f"${value / 1e9:.2f}B"
    elif abs(value) >= 1e6:
        return f"${value / 1e6:.2f}M"
    elif abs(value) >= 1e3:
        return f"${value / 1e3:.2f}K"
    else:
        return f"${value:.2f}"


def format_percentage(value):
    """Format decimal as percentage"""
    if value is None:
        return "N/A"
    return f"{value * 100:.2f}%"


def run_analysis(ticker):
    """Run both financial and sentiment analysis for a ticker"""
    print(f"\n🔍 Analyzing {ticker.upper()}...")
    print("=" * 50)

    # Get company name
    company_name = get_company_info_from_ticker(ticker)
    print(f"Company: {company_name}")

    # Financial Analysis
    if financial_available:
        print("\n📊 Financial Analysis:")
        try:
            raw_data = fetch_data(ticker)
            financial_data = process_data(raw_data)

            # Revenue Metrics
            print("  📈 REVENUE METRICS:")
            if financial_data.get('revenue_5y'):
                print(
                    f"    Latest Revenue (TTM): {format_currency(financial_data['revenue_5y'][0]) if financial_data['revenue_5y'][0] else 'N/A'}")
                # Show 5-year revenue history
                revenue_years = financial_data['revenue_5y'][:5]  # Last 5 years
                if len(revenue_years) > 1:
                    print("    Revenue (5-Year History):")
                    for i, rev in enumerate(revenue_years):
                        if rev:
                            print(f"      Year {i + 1}: {format_currency(rev)}")

            if financial_data.get('revenue_growth_rate'):
                print(f"    Revenue Growth Rate: {format_percentage(financial_data['revenue_growth_rate'])}")

            # Profitability Metrics
            print("\n  💰 PROFITABILITY METRICS:")
            if financial_data.get('ebitda_5y'):
                print(
                    f"    Latest EBITDA (TTM): {format_currency(financial_data['ebitda_5y'][0]) if financial_data['ebitda_5y'][0] else 'N/A'}")
                # Show 5-year EBITDA history
                ebitda_years = financial_data['ebitda_5y'][:5]
                if len(ebitda_years) > 1:
                    print("    EBITDA (5-Year History):")
                    for i, ebitda in enumerate(ebitda_years):
                        if ebitda:
                            print(f"      Year {i + 1}: {format_currency(ebitda)}")

            if financial_data.get('ebitda_growth_rate'):
                print(f"    EBITDA Growth Rate: {format_percentage(financial_data['ebitda_growth_rate'])}")

            if financial_data.get('ebit'):
                print(f"    EBIT: {format_currency(financial_data['ebit'])}")

            if financial_data.get('net_income'):
                print(f"    Net Income: {format_currency(financial_data['net_income'])}")

            if financial_data.get('gross_profit_margin'):
                print(f"    Gross Profit Margin: {format_percentage(financial_data['gross_profit_margin'])}")

            # Cash Flow & Investment Metrics
            print("\n  💸 CASH FLOW & INVESTMENT:")
            if financial_data.get('depreciation_amortization_5y'):
                da_years = financial_data['depreciation_amortization_5y'][:5]
                if da_years and da_years[0]:
                    print(f"    Latest D&A: {format_currency(da_years[0])}")
                    if len(da_years) > 1:
                        print("    D&A (5-Year History):")
                        for i, da in enumerate(da_years):
                            if da:
                                print(f"      Year {i + 1}: {format_currency(da)}")

            if financial_data.get('capex'):
                print(f"    CapEx: {format_currency(financial_data['capex'])}")

            if financial_data.get('working_capital'):
                print(f"    Working Capital: {format_currency(financial_data['working_capital'])}")

            if financial_data.get('levered_fcf'):
                print(f"    Levered Free Cash Flow: {format_currency(financial_data['levered_fcf'])}")

            if financial_data.get('tax_rate'):
                print(f"    Tax Rate: {format_percentage(financial_data['tax_rate'])}")

            # Financial Health Ratios
            print("\n  🏥 FINANCIAL HEALTH RATIOS:")
            if financial_data.get('debt_to_equity_ratio'):
                print(f"    Debt-to-Equity Ratio: {financial_data['debt_to_equity_ratio']:.2f}")

            if financial_data.get('current_ratio'):
                print(f"    Current Ratio: {financial_data['current_ratio']:.2f}")

            if financial_data.get('quick_ratio'):
                print(f"    Quick Ratio: {financial_data['quick_ratio']:.2f}")

            # Valuation Metrics
            print("\n  📊 VALUATION METRICS:")
            if financial_data.get('pe_ratio'):
                print(f"    P/E Ratio: {financial_data['pe_ratio']:.2f}")

            if financial_data.get('eps'):
                print(f"    EPS: ${financial_data['eps']:.2f}")

            if financial_data.get('ps_ratio'):
                print(f"    P/S Ratio: {financial_data['ps_ratio']:.2f}")

            if financial_data.get('ev_ebitda'):
                print(f"    EV/EBITDA: {financial_data['ev_ebitda']:.2f}")

            if financial_data.get('dividend_yield_percentage'):
                print(f"    Dividend Yield: {financial_data['dividend_yield_percentage']:.2f}%")

            # Cost of Capital (Note: WACC calculation needs work)
            print("\n  🏦 COST OF CAPITAL:")
            if financial_data.get('wacc'):
                print(f"    WACC: {financial_data['wacc']} (Note: Needs proper calculation)")
            else:
                print("    WACC: Not calculated (requires market data)")

            print("\n  ✅ Financial analysis completed")

        except Exception as e:
            print(f"  ❌ Financial analysis failed: {e}")
            import traceback
            traceback.print_exc()

    # Sentiment Analysis
    if sentiment_available:
        print("\n📰 Sentiment Analysis:")
        try:
            analyzer = SentimentAnalyzer()
            sentiment_result = analyzer.get_company_sentiment_analysis(company_name, ticker, days_back=3)

            # Display sentiment results
            sentiment_emoji = "📈" if sentiment_result['overall_sentiment'] == 'positive' else "📉" if sentiment_result[
                                                                                                         'overall_sentiment'] == 'negative' else "➡️"
            print(f"  Overall Sentiment: {sentiment_emoji} {sentiment_result['overall_sentiment'].title()}")
            print(f"  Sentiment Score: {sentiment_result['sentiment_score']:.3f}")
            print(f"  Articles Analyzed: {sentiment_result['articles_analyzed']}")

            # Show distribution
            dist = sentiment_result['sentiment_distribution']
            print(f"  Positive: {dist['positive']} | Negative: {dist['negative']} | Neutral: {dist['neutral']}")

            print("  ✅ Sentiment analysis completed")

        except Exception as e:
            print(f"  ❌ Sentiment analysis failed: {e}")


def main():
    print("🚀 Stock Analysis Tool")
    print("=" * 30)

    if not financial_available and not sentiment_available:
        print("❌ Neither financial.py nor sentiment.py could be imported!")
        return

    print("Available modules:")
    if financial_available:
        print("  ✅ Financial Analysis")
    if sentiment_available:
        print("  ✅ Sentiment Analysis")

    while True:
        print("\n" + "=" * 30)
        ticker = input("Enter ticker symbol (or 'quit' to exit): ").strip().upper()

        if ticker.lower() in ['quit', 'exit', 'q']:
            print("👋 Goodbye!")
            break

        if not ticker:
            print("Please enter a valid ticker symbol.")
            continue

        try:
            etl_for_ticker(ticker)
            print(f"Data for {ticker} processed and stored in the database.")
        except Exception as e:
            print(f"Error processing {ticker}: {e}")


if __name__ == "__main__":
    main()