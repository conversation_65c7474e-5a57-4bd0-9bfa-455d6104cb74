import psycopg2
import json
from datetime import date
from Preprocessing.financial import fetch_data, process_data
from Preprocessing.sentiment import Sentiment<PERSON><PERSON><PERSON><PERSON>, get_company_info_from_ticker
import math

DB_NAME = 'stocktrek_mainbranch'
DB_USER = 'stocktrek_admin'
DB_PASSWORD = 'equity_FR'
DB_HOST = 'localhost'
DB_PORT = '5432'

def clean_for_json(val):
    if isinstance(val, float) and (math.isnan(val) or math.isinf(val)):
        return None
    if isinstance(val, list):
        return [clean_for_json(x) for x in val]
    if isinstance(val, dict):
        return {k: clean_for_json(v) for k, v in val.items()}
    return val

def to_jsonb(val):
    if val is None:
        return None
    cleaned = clean_for_json(val)
    return json.dumps(cleaned)

def etl_for_ticker(ticker):
    company_name = get_company_info_from_ticker(ticker)
    print(f"Processing {company_name} ({ticker})")

    raw_financial = fetch_data(ticker)
    financials = process_data(raw_financial)

    analyzer = SentimentAnalyzer()
    sentiment_result = analyzer.get_company_sentiment_analysis(company_name, ticker, days_back=7)
    sentiment_json = json.dumps(sentiment_result)

    conn = psycopg2.connect(
        dbname=DB_NAME,
        user=DB_USER,
        password=DB_PASSWORD,
        host=DB_HOST,
        port=DB_PORT
    )
    cur = conn.cursor()

    cur.execute("SELECT id FROM companies WHERE ticker = %s", (ticker,))
    result = cur.fetchone()
    if result:
        company_id = result[0]
    else:
        cur.execute("INSERT INTO companies (name, ticker, symbol) VALUES (%s, %s, %s) RETURNING id", (company_name, ticker, ticker))
        company_id = cur.fetchone()[0]
        conn.commit()

    company_data = {
        'company_id': company_id,
        'revenue': to_jsonb(financials.get('revenue_5y')),
        'revenue_growth_rate_fwd': None, 
        'revenue_growth_rate_trailing': None,
        'ebitda': to_jsonb(financials.get('ebitda_5y')),
        'ebitda_growth_rate_fwd': None, 
        'ebitda_growth_rate_trailing': None, 
        'depreciation_amortization': to_jsonb(financials.get('depreciation_amortization_5y')),
        'ebit': to_jsonb(financials.get('ebit')),
        'capex': to_jsonb(financials.get('capex')),
        'working_capital': to_jsonb(financials.get('working_capital')),
        'tax_rate': financials.get('tax_rate'),
        'levered_fcf': to_jsonb(financials.get('levered_fcf')),
        'wacc': financials.get('wacc'),
        'debt_to_equity_ratio': financials.get('debt_to_equity_ratio'),
        'current_ratio': financials.get('current_ratio'),
        'quick_ratio': financials.get('quick_ratio'),
        'gross_profit_margin': financials.get('gross_profit_margin'),
        'pe_ratio': financials.get('pe_ratio'),
        'eps': financials.get('eps'),
        'ps_ratio': financials.get('ps_ratio'),
        'dividend_yield_percentage': financials.get('dividend_yield_percentage'),
        'ev_to_ebitda': financials.get('ev_ebitda'),
        'net_income': to_jsonb(financials.get('net_income')),
        'sentiment_data': sentiment_json,
        'as_of_date': date.today()
    }

    columns = ', '.join(company_data.keys())
    placeholders = ', '.join(['%s'] * len(company_data))
    values = list(company_data.values())
    cur.execute(f"INSERT INTO company_data ({columns}) VALUES ({placeholders})", values)
    conn.commit()
    print(f"Inserted data for {company_name} ({ticker})")
    cur.close()
    conn.close() 