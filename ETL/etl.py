import psycopg2
import json
from datetime import date
from Preprocessing.financial import fetch_data, process_data
from Preprocessing.sentiment import Sentiment<PERSON><PERSON><PERSON><PERSON>, get_company_info_from_ticker

# --- CONFIG ---
DB_NAME = 'your_db'
DB_USER = 'your_user'
DB_PASSWORD = 'your_password'
DB_HOST = 'localhost'
DB_PORT = '5432'

# --- ETL LOGIC ---
def etl_for_ticker(ticker):
    # 1. Extract company name
    company_name = get_company_info_from_ticker(ticker)
    print(f"Processing {company_name} ({ticker})")

    # 2. Extract financial data
    raw_financial = fetch_data(ticker)
    financials = process_data(raw_financial)

    # 3. Extract sentiment data
    analyzer = SentimentAnalyzer()
    sentiment_result = analyzer.get_company_sentiment_analysis(company_name, ticker, days_back=7)
    sentiment_json = json.dumps(sentiment_result)

    # 4. Connect to DB
    conn = psycopg2.connect(
        dbname=DB_NAME,
        user=DB_USER,
        password=DB_PASSWORD,
        host=DB_HOST,
        port=DB_PORT
    )
    cur = conn.cursor()

    # 5. Insert company if not exists
    cur.execute("SELECT id FROM companies WHERE ticker = %s", (ticker,))
    result = cur.fetchone()
    if result:
        company_id = result[0]
    else:
        cur.execute("INSERT INTO companies (name, ticker) VALUES (%s, %s) RETURNING id", (company_name, ticker))
        company_id = cur.fetchone()[0]
        conn.commit()

    # 6. Prepare data for company_data
    def to_jsonb(val):
        if val is None:
            return None
        if isinstance(val, (dict, list)):
            return json.dumps(val)
        return json.dumps({"TTM": val}) if not isinstance(val, str) else val

    company_data = {
        'company_id': company_id,
        'revenue': to_jsonb(financials.get('revenue_5y')),
        'revenue_growth_rate_fwd': None,  # Not calculated in process_data
        'revenue_growth_rate_trailing': None,  # Not calculated in process_data
        'ebitda': to_jsonb(financials.get('ebitda_5y')),
        'ebitda_growth_rate_fwd': None,  # Not calculated in process_data
        'ebitda_growth_rate_trailing': None,  # Not calculated in process_data
        'depreciation_amortization': to_jsonb(financials.get('depreciation_amortization_5y')),
        'ebit': to_jsonb(financials.get('ebit')),
        'capex': to_jsonb(financials.get('capex')),
        'working_capital': to_jsonb(financials.get('working_capital')),
        'tax_rate': financials.get('tax_rate'),
        'levered_fcf': to_jsonb(financials.get('levered_fcf')),
        'wacc': financials.get('wacc'),
        'debt_to_equity_ratio': financials.get('debt_to_equity_ratio'),
        'current_ratio': financials.get('current_ratio'),
        'quick_ratio': financials.get('quick_ratio'),
        'gross_profit_margin': financials.get('gross_profit_margin'),
        'pe_ratio': financials.get('pe_ratio'),
        'eps': financials.get('eps'),
        'ps_ratio': financials.get('ps_ratio'),
        'dividend_yield_percentage': financials.get('dividend_yield_percentage'),
        'ev_to_ebitda': financials.get('ev_ebitda'),
        'net_income': to_jsonb(financials.get('net_income')),
        'sentiment_data': sentiment_json,
        'as_of_date': date.today()
    }

    # 7. Insert into company_data
    columns = ', '.join(company_data.keys())
    placeholders = ', '.join(['%s'] * len(company_data))
    values = list(company_data.values())
    cur.execute(f"INSERT INTO company_data ({columns}) VALUES ({placeholders})", values)
    conn.commit()
    print(f"Inserted data for {company_name} ({ticker})")
    cur.close()
    conn.close() 