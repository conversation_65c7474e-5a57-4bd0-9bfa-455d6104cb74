#!/usr/bin/env python3
"""
Neural Network Training Script for StockTrek
Trains the neural network model with real market data
"""

import os
import sys
import numpy as np
import pandas as pd
import yfinance as yf
import joblib
import logging
from pathlib import Path
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, accuracy_score

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StockTrekTrainer:
    def __init__(self):
        self.models_dir = Path("models")
        self.models_dir.mkdir(exist_ok=True)
        self.model = None
        self.scaler = None
        self.label_encoder = None
        self.feature_names = None
        
    def extract_features(self, data):
        """Extract comprehensive features from stock data"""
        try:
            if len(data) < 60:
                return None
            
            features = {}
            
            # Price features
            current_price = data['Close'].iloc[-1]
            features['current_price'] = current_price
            features['price_change_1d'] = (data['Close'].iloc[-1] - data['Close'].iloc[-2]) / data['Close'].iloc[-2] * 100
            features['price_change_5d'] = (data['Close'].iloc[-1] - data['Close'].iloc[-6]) / data['Close'].iloc[-6] * 100
            features['price_change_20d'] = (data['Close'].iloc[-1] - data['Close'].iloc[-21]) / data['Close'].iloc[-21] * 100
            
            # Moving averages
            features['sma_10'] = data['Close'].rolling(10).mean().iloc[-1]
            features['sma_20'] = data['Close'].rolling(20).mean().iloc[-1]
            features['sma_50'] = data['Close'].rolling(50).mean().iloc[-1]
            features['ema_12'] = data['Close'].ewm(span=12).mean().iloc[-1]
            features['ema_26'] = data['Close'].ewm(span=26).mean().iloc[-1]
            
            # Technical indicators
            delta = data['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            features['rsi'] = 100 - (100 / (1 + rs)).iloc[-1]
            
            # MACD
            ema12 = data['Close'].ewm(span=12).mean()
            ema26 = data['Close'].ewm(span=26).mean()
            macd = ema12 - ema26
            signal = macd.ewm(span=9).mean()
            features['macd'] = macd.iloc[-1]
            features['macd_signal'] = signal.iloc[-1]
            features['macd_histogram'] = (macd - signal).iloc[-1]
            
            # Bollinger Bands
            sma20 = data['Close'].rolling(20).mean()
            std20 = data['Close'].rolling(20).std()
            features['bb_upper'] = (sma20 + 2 * std20).iloc[-1]
            features['bb_lower'] = (sma20 - 2 * std20).iloc[-1]
            features['bb_position'] = (current_price - features['bb_lower']) / (features['bb_upper'] - features['bb_lower'])
            
            # Volume features
            features['volume'] = data['Volume'].iloc[-1]
            features['volume_sma_20'] = data['Volume'].rolling(20).mean().iloc[-1]
            features['volume_ratio'] = features['volume'] / features['volume_sma_20']
            
            # Volatility
            features['volatility_20d'] = data['Close'].pct_change().rolling(20).std().iloc[-1] * np.sqrt(252)
            
            # Price position
            high_52w = data['High'].rolling(min(252, len(data))).max().iloc[-1]
            low_52w = data['Low'].rolling(min(252, len(data))).min().iloc[-1]
            features['price_position_52w'] = (current_price - low_52w) / (high_52w - low_52w) if high_52w != low_52w else 0.5
            
            # Additional momentum indicators
            features['price_momentum'] = (current_price / data['Close'].rolling(10).mean().iloc[-1] - 1) * 100
            features['volume_momentum'] = (features['volume'] / data['Volume'].rolling(10).mean().iloc[-1] - 1) * 100
            
            # Replace any NaN values with 0
            for key, value in features.items():
                if pd.isna(value) or np.isinf(value):
                    features[key] = 0.0
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            return None
    
    def collect_training_data(self):
        """Collect training data from multiple stocks"""
        training_symbols = [
            'AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA', 'AMZN', 'META', 'NFLX', 
            'AMD', 'INTC', 'CRM', 'ADBE', 'PYPL', 'UBER', 'ZOOM', 'SHOP',
            'SQ', 'ROKU', 'TWLO', 'OKTA', 'SNOW', 'PLTR', 'COIN', 'RBLX'
        ]
        
        training_data = []
        training_labels = []
        
        logger.info(f"📊 Collecting training data from {len(training_symbols)} stocks...")
        
        for i, symbol in enumerate(training_symbols):
            try:
                logger.info(f"Processing {symbol} ({i+1}/{len(training_symbols)})...")
                
                # Get 2 years of historical data
                ticker = yf.Ticker(symbol)
                hist_data = ticker.history(period="2y")
                
                if len(hist_data) < 100:
                    logger.warning(f"Insufficient data for {symbol}")
                    continue
                
                # Generate training samples
                for j in range(60, len(hist_data) - 30):  # Need 60 days lookback, 30 days forward
                    try:
                        # Extract features for day j
                        current_data = hist_data.iloc[:j+1]
                        features = self.extract_features(current_data)
                        
                        if features and len(features) > 20:
                            # Calculate future return (30 days ahead)
                            current_price = hist_data.iloc[j]['Close']
                            future_price = hist_data.iloc[j+30]['Close']
                            return_pct = (future_price - current_price) / current_price * 100
                            
                            # Create label based on return
                            if return_pct > 7:  # Strong buy signal
                                label = 'BUY'
                            elif return_pct < -7:  # Strong sell signal
                                label = 'SELL'
                            else:
                                label = 'HOLD'
                            
                            training_data.append(list(features.values()))
                            training_labels.append(label)
                            
                    except Exception as e:
                        continue
                        
            except Exception as e:
                logger.warning(f"Could not collect data for {symbol}: {e}")
                continue
        
        logger.info(f"✅ Collected {len(training_data)} training samples")
        return np.array(training_data), np.array(training_labels)
    
    def train_model(self):
        """Train the neural network model"""
        try:
            logger.info("🎓 Starting neural network training...")
            
            # Collect training data
            X, y = self.collect_training_data()
            
            if len(X) < 100:
                logger.error("❌ Insufficient training data collected")
                return False
            
            # Get feature names from a sample
            sample_ticker = yf.Ticker('AAPL')
            sample_data = sample_ticker.history(period="1y")
            sample_features = self.extract_features(sample_data)
            self.feature_names = list(sample_features.keys()) if sample_features else []
            
            logger.info(f"📊 Training with {len(X)} samples and {len(self.feature_names)} features")
            
            # Create and fit scaler
            self.scaler = StandardScaler()
            X_scaled = self.scaler.fit_transform(X)
            
            # Create and fit label encoder
            self.label_encoder = LabelEncoder()
            y_encoded = self.label_encoder.fit_transform(y)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
            )
            
            # Create ensemble model
            rf_model = RandomForestClassifier(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
            
            gb_model = GradientBoostingClassifier(
                n_estimators=150,
                max_depth=8,
                learning_rate=0.1,
                random_state=42
            )
            
            svm_model = SVC(
                kernel='rbf',
                probability=True,
                random_state=42,
                gamma='scale'
            )
            
            # Create ensemble
            self.model = VotingClassifier(
                estimators=[('rf', rf_model), ('gb', gb_model), ('svm', svm_model)],
                voting='soft'
            )
            
            # Train the model
            logger.info("🔧 Training ensemble model...")
            self.model.fit(X_train, y_train)
            
            # Evaluate model
            train_score = self.model.score(X_train, y_train)
            test_score = self.model.score(X_test, y_test)
            
            # Get predictions for detailed evaluation
            y_pred = self.model.predict(X_test)
            
            logger.info(f"📊 Training accuracy: {train_score:.3f}")
            logger.info(f"📊 Testing accuracy: {test_score:.3f}")
            
            # Print classification report
            target_names = self.label_encoder.classes_
            logger.info("📋 Classification Report:")
            print(classification_report(y_test, y_pred, target_names=target_names))
            
            # Save the trained model
            self.save_model()
            
            logger.info("✅ Neural network training completed successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Error training model: {e}")
            return False
    
    def save_model(self):
        """Save the trained model and preprocessors"""
        try:
            model_path = self.models_dir / 'optimized_stock_model.joblib'
            scaler_path = self.models_dir / 'feature_scaler.joblib'
            encoder_path = self.models_dir / 'label_encoder.joblib'
            features_path = self.models_dir / 'feature_names.joblib'
            
            joblib.dump(self.model, model_path)
            joblib.dump(self.scaler, scaler_path)
            joblib.dump(self.label_encoder, encoder_path)
            joblib.dump(self.feature_names, features_path)
            
            logger.info("💾 Model and preprocessors saved successfully")
            logger.info(f"📁 Model files saved to: {self.models_dir}")
            
        except Exception as e:
            logger.error(f"❌ Error saving model: {e}")

def main():
    """Main training function"""
    print("🚀 StockTrek Neural Network Training")
    print("=" * 60)
    
    trainer = StockTrekTrainer()
    
    if trainer.train_model():
        print("\n🎉 🎉 🎉 TRAINING COMPLETED SUCCESSFULLY! 🎉 🎉 🎉")
        print("🚀 Neural network model is now ready for predictions!")
        print("\n📋 Next steps:")
        print("   python3 main.py --predict AAPL --timeframe 30")
        print("   python3 main.py --predict NVDA --timeframe 7")
        return True
    else:
        print("\n❌ Training failed. Please check the logs above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
