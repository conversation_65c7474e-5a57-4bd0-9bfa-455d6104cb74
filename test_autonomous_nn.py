#!/usr/bin/env python3
"""
Test script for the autonomous neural network system
"""

import sys
import os
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_autonomous_neural_network():
    """Test the autonomous neural network system"""
    try:
        print("🚀 Testing Autonomous Neural Network System")
        print("=" * 60)
        
        # Test imports
        print("📦 Testing imports...")
        from src.autonomous_neural_network import AutonomousNeuralNetwork, AutonomousStockDiscovery, RobustFeatureExtractor
        from src.neural_network import NeuralNetworkPredictor
        print("✅ All imports successful")
        
        # Test stock discovery
        print("\n🔍 Testing stock discovery...")
        discovery = AutonomousStockDiscovery()
        print(f"📊 Cached stocks: {len(discovery.discovered_stocks)}")
        
        # Test feature extraction
        print("\n🔧 Testing feature extraction...")
        extractor = RobustFeatureExtractor()
        features = extractor.extract_features('AAPL', 30)
        if features:
            print(f"✅ Feature extraction successful: {len(features)} features")
            print(f"📋 Sample features: {list(features.keys())[:5]}...")
        else:
            print("❌ Feature extraction failed")
        
        # Test neural network predictor
        print("\n🧠 Testing neural network predictor...")
        predictor = NeuralNetworkPredictor()
        
        # Get model status
        status = predictor.get_model_status()
        print(f"📊 Model Status:")
        for key, value in status.items():
            print(f"   {key}: {value}")
        
        # Test prediction
        print("\n🎯 Testing prediction...")
        result = predictor.predict('AAPL', 30)
        print(f"📋 Prediction result:")
        for key, value in result.items():
            print(f"   {key}: {value}")
        
        print("\n🎉 All tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_autonomous_neural_network()
    sys.exit(0 if success else 1)
