import yfinance as yf
import requests
import pandas as pd
import numpy as np

FRED_API_KEY = "118ed465c7ed1ca1348760808d0e82b9"


def fetch_data(stock):
    ticker = yf.Ticker(stock)

    financial_data = {
        "ticker": ticker,
        "information": ticker.info,
        "financials": ticker.financials,
        "balance_sheet": ticker.balance_sheet,
        "cashflows": ticker.cashflow,
        "earnings": ticker.earnings,
        "sustainability": ticker.sustainability,
        "calendar": ticker.calendar,
    }
    return financial_data


def safe_get(data, key, default=None):
    try:
        if isinstance(data, dict):
            return data.get(key, default)
        elif hasattr(data, 'get'):
            return data.get(key, default)
        else:
            return default
    except:
        return default


def calculate_growth_rate(values):
    try:
        values = [v for v in values if v is not None and not pd.isna(v)]
        if len(values) < 2:
            return None

        start_val = values[-1]
        end_val = values[0]
        periods = len(values) - 1

        if start_val <= 0:
            return None

        growth_rate = ((end_val / start_val) ** (1 / periods)) - 1
        return growth_rate
    except:
        return None

def calculate_wacc(info, balance, income_statement):
    try:
        # Get market cap (market value of equity)
        market_cap = safe_get(info, 'marketCap', None)
        if not market_cap:
            shares_outstanding = safe_get(info, 'sharesOutstanding', None) or safe_get(info, 'impliedSharesOutstanding',
                                                                                       None)
            current_price = safe_get(info, 'currentPrice', None) or safe_get(info, 'regularMarketPrice', None)
            if shares_outstanding and current_price:
                market_cap = shares_outstanding * current_price

        if not market_cap:
            return None

        # Get total debt from balance sheet
        total_debt = 0
        if balance is not None and not balance.empty:
            debt_row = balance.loc[
                balance.index.str.contains('Total Debt|Long Term Debt|Short Term Debt', case=False, na=False)]
            if not debt_row.empty:
                total_debt = debt_row.iloc[0, 0] or 0

        total_value = market_cap + total_debt

        # Calculate weights
        weight_equity = market_cap / total_value
        weight_debt = total_debt / total_value if total_debt > 0 else 0

        # Calculate cost of equity using CAPM: Re = Rf + Beta * (Rm - Rf)
        beta = safe_get(info, 'beta', 1.0)  # Default to 1.0 if not available

        # Fetch risk-free rate from FRED API
        risk_free_rate = 0.04  # Default fallback
        try:
            fred_url = f"https://api.stlouisfed.org/fred/series/observations?series_id=GS10&api_key={FRED_API_KEY}&file_type=json&limit=1&sort_order=desc"
            response = requests.get(fred_url)
            if response.status_code == 200:
                data = response.json()
                if data['observations'] and data['observations'][0]['value'] != '.':
                    risk_free_rate = float(data['observations'][0]['value']) / 100
        except:
            pass

        market_risk_premium = 0.06  # Historical average market risk premium
        cost_of_equity = risk_free_rate + (beta * market_risk_premium)

        # Calculate cost of debt
        cost_of_debt = 0
        if total_debt > 0:
            # Try to get interest expense from income statement
            interest_expense = None
            if income_statement is not None and not income_statement.empty:
                interest_row = income_statement.loc[
                    income_statement.index.str.contains('Interest Expense|Interest', case=False, na=False)]
                if not interest_row.empty:
                    interest_expense = abs(interest_row.iloc[0, 0])

            if interest_expense and interest_expense > 0:
                cost_of_debt = interest_expense / total_debt
            else:
                cost_of_debt = 0.05  # Default 5% cost of debt

        # Get tax rate
        tax_rate = 0.25  # Default corporate tax rate
        if income_statement is not None and not income_statement.empty:
            try:
                tax_row = income_statement.loc[
                    income_statement.index.str.contains('Tax|Income Tax', case=False, na=False)]
                pretax_row = income_statement.loc[
                    income_statement.index.str.contains('Pretax Income|Income Before Tax', case=False, na=False)]
                if not tax_row.empty and not pretax_row.empty:
                    tax_expense = tax_row.iloc[0, 0]
                    pretax_income = pretax_row.iloc[0, 0]
                    if pretax_income and pretax_income > 0:
                        calculated_tax_rate = tax_expense / pretax_income
                        if 0 <= calculated_tax_rate <= 0.5:  # Reasonable range
                            tax_rate = calculated_tax_rate
            except:
                pass

        # Calculate WACC
        wacc = (weight_equity * cost_of_equity) + (weight_debt * cost_of_debt * (1 - tax_rate))

        return wacc

    except Exception as e:
        print(f"Error calculating WACC: {e}")
        return None


def process_data(raw_data):
    info = raw_data["information"]
    income_statement = raw_data["financials"]
    balance = raw_data["balance_sheet"]
    cashflow = raw_data["cashflows"]

    try:
        revenue_5y = []
        if income_statement is not None and not income_statement.empty:
            revenue_row = income_statement.loc[
                income_statement.index.str.contains('Total Revenue|Revenue', case=False, na=False)]
            if not revenue_row.empty:
                revenue_5y = revenue_row.iloc[0].tolist()[:5]  # Last 5 years

        revenue_growth = calculate_growth_rate(revenue_5y)

        ebitda_5y = []
        if income_statement is not None and not income_statement.empty:
            ebitda_row = income_statement.loc[income_statement.index.str.contains('EBITDA', case=False, na=False)]
            if not ebitda_row.empty:
                ebitda_5y = ebitda_row.iloc[0].tolist()[:5]
            else:
                try:
                    ebit_row = income_statement.loc[
                        income_statement.index.str.contains('EBIT|Operating Income', case=False, na=False)]
                    da_row = income_statement.loc[
                        income_statement.index.str.contains('Depreciation|Amortization', case=False, na=False)]
                    if not ebit_row.empty and not da_row.empty:
                        ebit_vals = ebit_row.iloc[0].tolist()[:5]
                        da_vals = da_row.iloc[0].tolist()[:5]
                        ebitda_5y = [e + d if e is not None and d is not None else None
                                     for e, d in zip(ebit_vals, da_vals)]
                except:
                    pass

        ebitda_growth = calculate_growth_rate(ebitda_5y)

        da_5y = []
        if cashflow is not None and not cashflow.empty:
            da_row = cashflow.loc[cashflow.index.str.contains('Depreciation|Amortization', case=False, na=False)]
            if not da_row.empty:
                da_5y = da_row.iloc[0].tolist()[:5]

        ebit = None
        if income_statement is not None and not income_statement.empty:
            ebit_row = income_statement.loc[
                income_statement.index.str.contains('EBIT|Operating Income', case=False, na=False)]
            if not ebit_row.empty:
                ebit = ebit_row.iloc[0, 0]

        capex = None
        if cashflow is not None and not cashflow.empty:
            capex_row = cashflow.loc[
                cashflow.index.str.contains('Capital Expenditure|Property Plant Equipment', case=False, na=False)]
            if not capex_row.empty:
                capex = abs(capex_row.iloc[0, 0])

        working_capital = None
        if balance is not None and not balance.empty:
            try:
                current_assets_row = balance.loc[balance.index.str.contains('Current Assets', case=False, na=False)]
                current_liab_row = balance.loc[balance.index.str.contains('Current Liabilities', case=False, na=False)]
                if not current_assets_row.empty and not current_liab_row.empty:
                    current_assets = current_assets_row.iloc[0, 0]
                    current_liab = current_liab_row.iloc[0, 0]
                    working_capital = current_assets - current_liab
            except:
                pass

        tax_rate = None
        if income_statement is not None and not income_statement.empty:
            try:
                tax_row = income_statement.loc[
                    income_statement.index.str.contains('Tax|Income Tax', case=False, na=False)]
                pretax_row = income_statement.loc[
                    income_statement.index.str.contains('Pretax Income|Income Before Tax', case=False, na=False)]
                if not tax_row.empty and not pretax_row.empty:
                    tax_expense = tax_row.iloc[0, 0]
                    pretax_income = pretax_row.iloc[0, 0]
                    if pretax_income and pretax_income != 0:
                        tax_rate = tax_expense / pretax_income
            except:
                pass

        levered_fcf = None
        if cashflow is not None and not cashflow.empty:
            fcf_row = cashflow.loc[cashflow.index.str.contains('Free Cash Flow', case=False, na=False)]
            if not fcf_row.empty:
                levered_fcf = fcf_row.iloc[0, 0]

        # Calculate WACC properly
        wacc = calculate_wacc(info, balance, income_statement)

        debt_to_equity = None
        if balance is not None and not balance.empty:
            try:
                debt_row = balance.loc[balance.index.str.contains('Total Debt|Long Term Debt', case=False, na=False)]
                equity_row = balance.loc[
                    balance.index.str.contains('Stockholder.*Equity|Total Equity', case=False, na=False)]
                if not debt_row.empty and not equity_row.empty:
                    total_debt = debt_row.iloc[0, 0]
                    total_equity = equity_row.iloc[0, 0]
                    if total_equity and total_equity != 0:
                        debt_to_equity = total_debt / total_equity
            except:
                pass

        current_ratio = None
        if balance is not None and not balance.empty:
            try:
                current_assets_row = balance.loc[balance.index.str.contains('Current Assets', case=False, na=False)]
                current_liab_row = balance.loc[balance.index.str.contains('Current Liabilities', case=False, na=False)]
                if not current_assets_row.empty and not current_liab_row.empty:
                    current_assets = current_assets_row.iloc[0, 0]
                    current_liab = current_liab_row.iloc[0, 0]
                    if current_liab and current_liab != 0:
                        current_ratio = current_assets / current_liab
            except:
                pass

        quick_ratio = None
        if balance is not None and not balance.empty:
            try:
                current_assets_row = balance.loc[balance.index.str.contains('Current Assets', case=False, na=False)]
                inventory_row = balance.loc[balance.index.str.contains('Inventory', case=False, na=False)]
                current_liab_row = balance.loc[balance.index.str.contains('Current Liabilities', case=False, na=False)]

                if not current_assets_row.empty and not current_liab_row.empty:
                    current_assets = current_assets_row.iloc[0, 0]
                    inventory = inventory_row.iloc[0, 0] if not inventory_row.empty else 0
                    current_liab = current_liab_row.iloc[0, 0]

                    if current_liab and current_liab != 0:
                        quick_ratio = (current_assets - inventory) / current_liab
            except:
                pass

        gross_profit_margin = None
        if income_statement is not None and not income_statement.empty:
            try:
                gross_profit_row = income_statement.loc[
                    income_statement.index.str.contains('Gross Profit', case=False, na=False)]
                revenue_row = income_statement.loc[
                    income_statement.index.str.contains('Total Revenue|Revenue', case=False, na=False)]
                if not gross_profit_row.empty and not revenue_row.empty:
                    gross_profit = gross_profit_row.iloc[0, 0]
                    revenue = revenue_row.iloc[0, 0]
                    if revenue and revenue != 0:
                        gross_profit_margin = gross_profit / revenue
            except:
                pass

        pe_ratio = safe_get(info, 'trailingPE', None) or safe_get(info, 'forwardPE', None)

        eps = safe_get(info, 'trailingEps', None) or safe_get(info, 'forwardEps', None)

        ps_ratio = safe_get(info, 'priceToSalesTrailing12Months', None)

        dividend_yield = safe_get(info, 'dividendYield', None)
        if dividend_yield:
            dividend_yield *= 100

        ev_ebitda = safe_get(info, 'enterpriseToEbitda', None)

        net_income = None
        if income_statement is not None and not income_statement.empty:
            net_income_row = income_statement.loc[
                income_statement.index.str.contains('Net Income', case=False, na=False)]
            if not net_income_row.empty:
                net_income = net_income_row.iloc[0, 0]

        processed_data = {
            "revenue_5y": revenue_5y,
            "revenue_growth_rate": revenue_growth,
            "ebitda_5y": ebitda_5y,
            "ebitda_growth_rate": ebitda_growth,
            "depreciation_amortization_5y": da_5y,
            "ebit": ebit,
            "capex": capex,
            "working_capital": working_capital,
            "tax_rate": tax_rate,
            "levered_fcf": levered_fcf,
            "wacc": wacc,
            "debt_to_equity_ratio": debt_to_equity,
            "current_ratio": current_ratio,
            "quick_ratio": quick_ratio,
            "gross_profit_margin": gross_profit_margin,
            "pe_ratio": pe_ratio,
            "eps": eps,
            "ps_ratio": ps_ratio,
            "dividend_yield_percentage": dividend_yield,
            "ev_ebitda": ev_ebitda,
            "net_income": net_income
        }

    except Exception as e:
        print(f"Error processing data: {e}")
        processed_data = {}

    return processed_data