import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import re
from textblob import TextBlob
from collections import defaultdict
import time

NEWS_API_KEY = "f47df3429d094b2095284582f1326292"


class SentimentAnalyzer:
    def __init__(self, api_key=NEWS_API_KEY):
        self.api_key = api_key
        self.base_url = "https://newsapi.org/v2"

        # Common financial sentiment keywords
        self.positive_keywords = [
            'profit', 'growth', 'earnings beat', 'bullish', 'upgrade', 'outperform',
            'strong results', 'revenue growth', 'expansion', 'acquisition', 'merger',
            'breakthrough', 'innovation', 'partnership', 'dividend increase', 'buyback'
        ]

        self.negative_keywords = [
            'loss', 'decline', 'bearish', 'downgrade', 'underperform', 'weak results',
            'revenue miss', 'layoffs', 'lawsuit', 'investigation', 'scandal',
            'bankruptcy', 'debt', 'recession', 'crisis', 'volatility'
        ]

    def fetch_company_news(self, company_name, ticker_symbol, days_back=7, page_size=100):
        """
        Fetch news specifically about a company using multiple search strategies
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)

        # Multiple search queries to ensure comprehensive coverage
        search_queries = [
            f'"{company_name}"',
            f'"{ticker_symbol}"',
            f'{company_name} earnings',
            f'{company_name} stock',
            f'{ticker_symbol} stock'
        ]

        all_articles = []

        for query in search_queries:
            try:
                # Add delay to respect API rate limits
                time.sleep(0.1)

                params = {
                    'q': query,
                    'apiKey': self.api_key,
                    'language': 'en',
                    'sortBy': 'publishedAt',
                    'from': start_date.strftime('%Y-%m-%d'),
                    'to': end_date.strftime('%Y-%m-%d'),
                    'pageSize': min(page_size, 100)  # API limit is 100
                }

                response = requests.get(f"{self.base_url}/everything", params=params)
                response.raise_for_status()

                data = response.json()

                if data['status'] == 'ok':
                    articles = data.get('articles', [])
                    # Filter articles to ensure they're actually about the company
                    filtered_articles = self._filter_relevant_articles(
                        articles, company_name, ticker_symbol
                    )
                    all_articles.extend(filtered_articles)

            except requests.exceptions.RequestException as e:
                print(f"Error fetching news for query '{query}': {e}")
                continue
            except Exception as e:
                print(f"Unexpected error for query '{query}': {e}")
                continue

        # Remove duplicates based on URL
        unique_articles = []
        seen_urls = set()
        for article in all_articles:
            if article['url'] not in seen_urls:
                unique_articles.append(article)
                seen_urls.add(article['url'])

        return unique_articles

    def _filter_relevant_articles(self, articles, company_name, ticker_symbol):
        """
        Filter articles to ensure they're actually about the target company
        """
        relevant_articles = []

        # Create variations of company name and ticker for matching
        company_variations = [
            company_name.lower(),
            ticker_symbol.lower(),
            ticker_symbol.upper(),
        ]

        # Add common variations (e.g., "Apple Inc" -> ["Apple", "AAPL"])
        if " " in company_name:
            company_variations.extend([word.lower() for word in company_name.split()])

        for article in articles:
            title = article.get('title', '').lower()
            description = article.get('description', '').lower()
            content = article.get('content', '').lower()

            # Combine all text for relevance checking
            full_text = f"{title} {description} {content}"

            # Check if any company variation appears in the article
            is_relevant = any(variation in full_text for variation in company_variations)

            # Additional check: ensure it's not just a passing mention
            if is_relevant:
                # Count occurrences to ensure it's a substantial mention
                mention_count = sum(full_text.count(variation) for variation in company_variations)
                if mention_count >= 1:  # At least one substantial mention
                    relevant_articles.append(article)

        return relevant_articles

    def analyze_sentiment(self, text):
        """
        Analyze sentiment using TextBlob with financial keyword enhancement
        """
        if not text or text.strip() == "":
            return {
                'polarity': 0.0,
                'subjectivity': 0.0,
                'sentiment_label': 'neutral',
                'confidence': 0.0,
                'keyword_sentiment': 0.0
            }

        # Clean text
        cleaned_text = self._clean_text(text)

        # TextBlob analysis
        blob = TextBlob(cleaned_text)
        polarity = blob.sentiment.polarity
        subjectivity = blob.sentiment.subjectivity

        # Keyword-based sentiment enhancement
        keyword_sentiment = self._calculate_keyword_sentiment(cleaned_text)

        # Combine TextBlob and keyword sentiment (weighted average)
        combined_sentiment = (polarity * 0.7) + (keyword_sentiment * 0.3)

        # Determine sentiment label and confidence
        if combined_sentiment > 0.1:
            sentiment_label = 'positive'
            confidence = min(abs(combined_sentiment), 1.0)
        elif combined_sentiment < -0.1:
            sentiment_label = 'negative'
            confidence = min(abs(combined_sentiment), 1.0)
        else:
            sentiment_label = 'neutral'
            confidence = 1.0 - abs(combined_sentiment)

        return {
            'polarity': combined_sentiment,
            'subjectivity': subjectivity,
            'sentiment_label': sentiment_label,
            'confidence': confidence,
            'keyword_sentiment': keyword_sentiment,
            'textblob_polarity': polarity
        }

    def _clean_text(self, text):
        if not text:
            return ""

        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', text)

        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)

        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    def _calculate_keyword_sentiment(self, text):
        text_lower = text.lower()

        positive_count = sum(1 for keyword in self.positive_keywords if keyword in text_lower)
        negative_count = sum(1 for keyword in self.negative_keywords if keyword in text_lower)

        total_keywords = positive_count + negative_count

        if total_keywords == 0:
            return 0.0

        # Calculate sentiment score (-1 to 1)
        sentiment_score = (positive_count - negative_count) / total_keywords
        return sentiment_score

    def get_company_sentiment_analysis(self, company_name, ticker_symbol, days_back=7):
        print(f"Fetching news for {company_name} ({ticker_symbol})...")

        # Fetch news articles
        articles = self.fetch_company_news(company_name, ticker_symbol, days_back)

        if not articles:
            print(f"No articles found for {company_name}")
            return {
                'company': company_name,
                'ticker': ticker_symbol,
                'articles_analyzed': 0,
                'overall_sentiment': 'neutral',
                'sentiment_score': 0.0,
                'confidence': 0.0,
                'sentiment_distribution': {'positive': 0, 'negative': 0, 'neutral': 0},
                'articles': []
            }

        print(f"Analyzing sentiment for {len(articles)} articles...")

        # Analyze sentiment for each article
        article_sentiments = []
        sentiment_scores = []
        sentiment_labels = []

        for article in articles:
            # Combine title and description for analysis
            text_to_analyze = f"{article.get('title', '')} {article.get('description', '')}"

            sentiment_result = self.analyze_sentiment(text_to_analyze)

            article_data = {
                'title': article.get('title', ''),
                'url': article.get('url', ''),
                'published_at': article.get('publishedAt', ''),
                'source': article.get('source', {}).get('name', ''),
                'sentiment': sentiment_result
            }

            article_sentiments.append(article_data)
            sentiment_scores.append(sentiment_result['polarity'])
            sentiment_labels.append(sentiment_result['sentiment_label'])

        # Calculate overall sentiment metrics
        overall_sentiment_score = np.mean(sentiment_scores) if sentiment_scores else 0.0
        overall_confidence = np.mean([art['sentiment']['confidence'] for art in article_sentiments])

        # Determine overall sentiment label
        if overall_sentiment_score > 0.1:
            overall_sentiment = 'positive'
        elif overall_sentiment_score < -0.1:
            overall_sentiment = 'negative'
        else:
            overall_sentiment = 'neutral'

        # Calculate sentiment distribution
        sentiment_distribution = {
            'positive': sentiment_labels.count('positive'),
            'negative': sentiment_labels.count('negative'),
            'neutral': sentiment_labels.count('neutral')
        }

        return {
            'company': company_name,
            'ticker': ticker_symbol,
            'articles_analyzed': len(articles),
            'overall_sentiment': overall_sentiment,
            'sentiment_score': overall_sentiment_score,
            'confidence': overall_confidence,
            'sentiment_distribution': sentiment_distribution,
            'daily_sentiment_trend': self._calculate_daily_sentiment_trend(article_sentiments),
            'articles': article_sentiments
        }

    def _calculate_daily_sentiment_trend(self, article_sentiments):
        daily_sentiments = defaultdict(list)

        for article in article_sentiments:
            pub_date = article['published_at'][:10]  # Extract date (YYYY-MM-DD)
            daily_sentiments[pub_date].append(article['sentiment']['polarity'])

        daily_trends = {}
        for date, sentiments in daily_sentiments.items():
            daily_trends[date] = {
                'average_sentiment': np.mean(sentiments),
                'article_count': len(sentiments)
            }

        return daily_trends


def get_company_info_from_ticker(ticker_symbol):
    try:
        import yfinance as yf
        ticker = yf.Ticker(ticker_symbol)
        info = ticker.info

        # Try different fields for company name
        company_name = (
                info.get('longName') or
                info.get('shortName') or
                info.get('companyName') or
                ticker_symbol.upper()
        )

        return company_name
    except:
        # Fallback to ticker symbol if yfinance fails
        return ticker_symbol.upper()


# Example usage
if __name__ == "__main__":
    analyzer = SentimentAnalyzer()

    # Example: Analyze Apple's sentiment
    company_name = "Apple Inc"
    ticker_symbol = "AAPL"

    result = analyzer.get_company_sentiment_analysis(company_name, ticker_symbol, days_back=3)

    print(f"\nSentiment Analysis for {result['company']} ({result['ticker']}):")
    print(f"Articles Analyzed: {result['articles_analyzed']}")
    print(f"Overall Sentiment: {result['overall_sentiment'].title()}")
    print(f"Sentiment Score: {result['sentiment_score']:.3f}")
    print(f"Confidence: {result['confidence']:.3f}")
    print(f"Sentiment Distribution: {result['sentiment_distribution']}")