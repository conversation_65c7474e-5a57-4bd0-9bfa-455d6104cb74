# StockTrek Complete Setup Guide

## 🚀 Quick Setup (Recommended)

### Step 1: Install Dependencies
```bash
# Install core dependencies
pip3 install yfinance pandas numpy scikit-learn psycopg2-binary textblob nltk python-dateutil pytz

# Optional: Install additional ML libraries
pip3 install tensorflow matplotlib seaborn
```

### Step 2: Test the System
```bash
# Quick test with live data
python3 quick_test.py

# Full system test
python3 test_live_system.py
```

### Step 3: Make Your First Prediction
```bash
# Predict Apple stock for 30 days
python3 main.py --predict AAPL --timeframe 30

# Predict Tesla for 60 days  
python3 main.py --predict TSLA --timeframe 60
```

### Step 4: Start Daemon Mode (Optional)
```bash
# Start autonomous 24/7 operation
python3 main.py --daemon
```

## 🔧 Detailed Setup

### Virtual Environment Setup (Optional but Recommended)
```bash
# Create virtual environment
python3 -m venv stocktrek_env

# Activate it
source stocktrek_env/bin/activate  # On macOS/Linux
# or
stocktrek_env\Scripts\activate     # On Windows

# Install dependencies
pip install -r requirements.txt
```

### Database Setup (Optional - for full features)
```bash
# Install PostgreSQL (if not installed)
brew install postgresql          # macOS
sudo apt install postgresql      # Ubuntu
# or download from postgresql.org

# Start PostgreSQL service
brew services start postgresql   # macOS
sudo systemctl start postgresql  # Ubuntu

# Create database and user
psql postgres
```

```sql
CREATE DATABASE stocktrek_mainbranch;
CREATE USER stocktrek_admin WITH PASSWORD 'equity_FR';
GRANT ALL PRIVILEGES ON DATABASE stocktrek_mainbranch TO stocktrek_admin;
\q
```

## 🧪 Testing with Live Data

### Basic Test
```bash
python3 quick_test.py
```

Expected output:
```
🚀 StockTrek Quick Test
========================================
🧪 Testing basic StockTrek functionality...
📊 Testing yfinance and live data...
✅ Live data test passed
   Company: Apple Inc.
   Current Price: $195.50
   Data points: 5
🧠 Testing neural network import...
✅ Neural network imported successfully
🔧 Testing feature extraction...
✅ Feature extraction successful (45 features)
   current_price: 195.500
   rsi: 67.234
   volatility: 0.234
🔮 Testing prediction...
✅ Prediction successful
   Prediction: BUY
   Confidence: 87.3%
   Target Price: $210.25

🎉 All basic tests passed!
```

### Comprehensive Test
```bash
python3 test_live_system.py
```

This will test:
- Live data fetching from multiple sources
- Neural network predictions with real data
- Market hours awareness
- CLI interface functionality
- ETL pipeline with live data

## 📊 Usage Examples

### Stock Predictions
```bash
# Basic prediction
python3 main.py --predict AAPL --timeframe 30

# Multiple timeframes
python3 main.py --predict MSFT --timeframe 7
python3 main.py --predict GOOGL --timeframe 60
python3 main.py --predict TSLA --timeframe 90
```

### Backtesting
```bash
# Test prediction accuracy
python3 main.py --backtest AAPL --days 90
python3 main.py --backtest TSLA --days 60
```

### Training
```bash
# Train/retrain the model
python3 main.py --train
```

### Daemon Mode
```bash
# Start autonomous operation
python3 main.py --daemon

# Check daemon status (in another terminal)
python3 -c "
from src.daemon import StockTrekDaemon
from src.neural_network import NeuralNetworkPredictor
from src.data_manager import DataManager
from src.backtesting import BacktestEngine

predictor = NeuralNetworkPredictor()
data_manager = DataManager()
backtest_engine = BacktestEngine(predictor, data_manager)
daemon = StockTrekDaemon(predictor, data_manager, backtest_engine)

status = daemon.get_daemon_status()
print(f'Daemon running: {status[\"running\"]}')
print(f'Predictions made: {status[\"predictions_made\"]}')
"
```

## 🎯 Expected Prediction Output

```
============================================================
🎯 STOCKTREK PREDICTION RESULTS
============================================================
📈 Ticker: AAPL
⏰ Timeframe: 30 days
📅 Analysis Date: 2024-12-30 10:30:00
------------------------------------------------------------
🚀 Prediction: BUY
🎯 Confidence: 87.3%
💰 Current Price: $195.50
🎯 Target Price: $210.25
📊 Expected Return: 7.5%
🤖 Model Certainty: High
------------------------------------------------------------
📊 Technical Analysis:
   RSI: 67.2 (Neutral)
   MACD: Bullish Signal
   Bollinger Bands: Near Upper Band
   Volume: Above Average
------------------------------------------------------------
📈 Fundamental Factors:
   P/E Ratio: 28.5
   Revenue Growth: 8.2%
   Profit Margin: 25.1%
   Market Cap: $3.1T
------------------------------------------------------------
⚠️  Risk Assessment: Medium
💡 Recommendation: Consider position sizing
============================================================
```

## 🔍 Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   # Install missing dependencies
   pip3 install yfinance pandas numpy scikit-learn
   ```

2. **Data Fetch Errors**
   ```bash
   # Test internet connection and yfinance
   python3 -c "import yfinance as yf; print(yf.Ticker('AAPL').history(period='1d'))"
   ```

3. **Database Connection Errors**
   ```bash
   # Check PostgreSQL is running
   brew services list | grep postgresql  # macOS
   sudo systemctl status postgresql      # Ubuntu
   ```

4. **Permission Errors**
   ```bash
   # Fix file permissions
   chmod +x *.sh
   chmod +x *.py
   ```

### Verification Commands

```bash
# Check Python version (3.8+ required)
python3 --version

# Check dependencies
python3 -c "import yfinance, pandas, numpy, sklearn; print('All dependencies OK')"

# Test live data
python3 -c "import yfinance as yf; print(yf.Ticker('AAPL').info['longName'])"

# Test neural network
python3 -c "from src.neural_network import NeuralNetworkPredictor; print('Neural network OK')"
```

## 🚀 Production Deployment

### For 24/7 Operation
```bash
# Start daemon in background
nohup python3 main.py --daemon > stocktrek.log 2>&1 &

# Monitor logs
tail -f stocktrek.log

# Stop daemon
pkill -f "python3 main.py --daemon"
```

### Performance Optimization
- Use SSD storage for faster data access
- Ensure stable internet connection for live data
- Monitor memory usage during training
- Set up log rotation for long-term operation

## 📋 System Requirements

- **Python**: 3.8 or higher
- **Memory**: 4GB RAM minimum, 8GB recommended
- **Storage**: 2GB free space
- **Internet**: Stable connection for live data
- **OS**: macOS, Linux, or Windows

## 🎉 You're Ready!

Your StockTrek system is now fully configured and ready for production use with live, up-to-date market data!

For support, run the test scripts and check the logs for any issues.
