#!/usr/bin/env python3
"""
Quick test script to trigger neural network training by making multiple predictions
"""

import subprocess
import sys

# List of stocks to test
test_stocks = [
    ("MSFT", 14),
    ("GOOGL", 21), 
    ("AMZN", 30),
    ("NVDA", 7),
    ("META", 14),
    ("NFLX", 21),
    ("JPM", 30),
    ("BAC", 7),
    ("V", 14),
    ("MA", 21)
]

print("🧪 Testing Neural Network Training with Multiple Predictions")
print("=" * 60)

for i, (ticker, timeframe) in enumerate(test_stocks, 1):
    print(f"\n[{i}/{len(test_stocks)}] Testing {ticker} with {timeframe} day timeframe...")
    
    try:
        # Run prediction
        result = subprocess.run([
            sys.executable, "main.py", 
            "--predict", ticker, 
            "--timeframe", str(timeframe)
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            # Extract key info from output
            lines = result.stdout.split('\n')
            action = None
            confidence = None
            model_type = None
            training_samples = None
            
            for line in lines:
                if "Action:" in line:
                    action = line.split("Action:")[-1].strip()
                elif "Confidence:" in line:
                    confidence = line.split("Confidence:")[-1].strip()
                elif "Model Type:" in line:
                    model_type = line.split("Model Type:")[-1].strip()
                elif "Training Samples:" in line:
                    training_samples = line.split("Training Samples:")[-1].strip()
            
            print(f"   ✅ {ticker}: {action} ({confidence}) | Model: {model_type} | Samples: {training_samples}")
            
        else:
            print(f"   ❌ {ticker}: Error - {result.stderr.strip()}")
            
    except subprocess.TimeoutExpired:
        print(f"   ⏰ {ticker}: Timeout")
    except Exception as e:
        print(f"   💥 {ticker}: Exception - {e}")

print(f"\n🎯 Neural Network Training Test Complete!")
print("Check the logs to see if the model started training after 10+ samples.")
