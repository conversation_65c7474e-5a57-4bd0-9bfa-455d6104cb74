#!/usr/bin/env python3
"""
StockTrek Setup Script
Installs dependencies and sets up the system
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")
    
    # Upgrade pip first
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip"):
        return False
    
    # Install requirements
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing requirements"):
        return False
    
    return True

def setup_database():
    """Set up PostgreSQL database"""
    print("🗄️ Setting up database...")
    
    print("Database setup requires manual configuration:")
    print("1. Install PostgreSQL if not already installed")
    print("2. Create database: stocktrek_mainbranch")
    print("3. Create user: stocktrek_admin with password: equity_FR")
    print("4. Grant privileges to the user")
    
    print("\nSQL commands to run:")
    print("CREATE DATABASE stocktrek_mainbranch;")
    print("CREATE USER stocktrek_admin WITH PASSWORD 'equity_FR';")
    print("GRANT ALL PRIVILEGES ON DATABASE stocktrek_mainbranch TO stocktrek_admin;")
    
    return True

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")
    
    directories = [
        "models",
        "logs",
        "data",
        "backups"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True

def test_installation():
    """Test the installation"""
    print("🧪 Testing installation...")
    
    try:
        result = subprocess.run([sys.executable, "test_stocktrek.py"], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Installation test passed")
            return True
        else:
            print("❌ Installation test failed")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Installation test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 StockTrek Setup")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    
    print(f"✅ Python version: {sys.version}")
    
    # Setup steps
    steps = [
        ("Create directories", create_directories),
        ("Install dependencies", install_dependencies),
        ("Setup database", setup_database),
        ("Test installation", test_installation),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        if not step_func():
            print(f"❌ Setup failed at: {step_name}")
            return False
    
    print("\n" + "=" * 50)
    print("🎉 StockTrek setup completed successfully!")
    
    print("\n📋 Next steps:")
    print("1. Configure database connection if needed")
    print("2. Test prediction: python3 main.py --predict AAPL --timeframe 30")
    print("3. Run backtest: python3 main.py --backtest TSLA --days 90")
    print("4. Start daemon: python3 main.py --daemon")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
