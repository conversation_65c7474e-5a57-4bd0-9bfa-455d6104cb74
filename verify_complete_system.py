#!/usr/bin/env python3
"""
Complete StockTrek System Verification
Tests everything with LIVE, UP-TO-DATE data to ensure 100% functionality
"""

import sys
import os
import time
import subprocess
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def print_header(title):
    """Print formatted header"""
    print(f"\n{'='*60}")
    print(f"🎯 {title}")
    print(f"{'='*60}")

def print_step(step, description):
    """Print formatted step"""
    print(f"\n📋 Step {step}: {description}")
    print("-" * 50)

def test_dependencies():
    """Test all required dependencies"""
    print_step(1, "Testing Dependencies")
    
    dependencies = [
        ('yfinance', 'Live market data'),
        ('pandas', 'Data processing'),
        ('numpy', 'Numerical computing'),
        ('sklearn', 'Machine learning'),
        ('textblob', 'Sentiment analysis'),
        ('datetime', 'Date/time handling'),
        ('json', 'Data serialization'),
        ('logging', 'System logging'),
    ]
    
    failed = []
    
    for dep, description in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep:12} - {description}")
        except ImportError:
            print(f"❌ {dep:12} - {description} (MISSING)")
            failed.append(dep)
    
    if failed:
        print(f"\n⚠️ Missing dependencies: {', '.join(failed)}")
        print("Install with: pip3 install " + " ".join(failed))
        return False
    
    print("\n🎉 All dependencies available!")
    return True

def test_live_market_data():
    """Test live market data fetching"""
    print_step(2, "Testing Live Market Data")
    
    try:
        import yfinance as yf
        
        # Test multiple tickers
        tickers = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
        
        for ticker in tickers:
            print(f"📊 Testing {ticker}...")
            
            stock = yf.Ticker(ticker)
            hist = stock.history(period="5d")
            info = stock.info
            
            if hist.empty:
                print(f"  ❌ No data for {ticker}")
                return False
            
            # Check data freshness
            latest_date = hist.index[-1].date()
            days_old = (datetime.now().date() - latest_date).days
            
            current_price = hist['Close'].iloc[-1]
            company_name = info.get('longName', 'Unknown')
            
            print(f"  ✅ {company_name}")
            print(f"     Price: ${current_price:.2f}")
            print(f"     Data age: {days_old} days")
            
            if days_old > 7:
                print(f"  ⚠️ Data may be stale (market closed?)")
            
            time.sleep(0.5)  # Rate limiting
        
        print("\n🎉 Live market data test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Live market data test failed: {e}")
        return False

def test_neural_network():
    """Test neural network functionality"""
    print_step(3, "Testing Neural Network")
    
    try:
        from src.neural_network import NeuralNetworkPredictor
        
        print("🧠 Initializing neural network...")
        predictor = NeuralNetworkPredictor()
        print("✅ Neural network initialized")
        
        # Test feature extraction
        print("🔧 Testing feature extraction with live data...")
        features = predictor.extract_comprehensive_features('AAPL', 30)
        
        if not features:
            print("❌ Feature extraction failed")
            return False
        
        print(f"✅ Extracted {len(features)} features")
        
        # Show key features
        key_features = ['current_price', 'rsi', 'volatility', 'price_momentum']
        for feature in key_features:
            if feature in features:
                print(f"   {feature}: {features[feature]:.3f}")
        
        # Test prediction
        print("🔮 Testing prediction generation...")
        prediction = predictor.predict_stock_movement('AAPL', 30)
        
        if 'error' in prediction:
            print(f"❌ Prediction failed: {prediction['error']}")
            return False
        
        print("✅ Prediction generated successfully")
        print(f"   Prediction: {prediction.get('prediction', 'N/A')}")
        print(f"   Confidence: {prediction.get('confidence', 0):.1f}%")
        
        price_targets = prediction.get('price_targets', {})
        if price_targets:
            print(f"   Target Price: ${price_targets.get('target_price', 0):.2f}")
        
        print("\n🎉 Neural network test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Neural network test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cli_interface():
    """Test CLI interface"""
    print_step(4, "Testing CLI Interface")
    
    try:
        # Test help command
        print("💻 Testing help command...")
        result = subprocess.run([sys.executable, "main.py", "--help"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            print("❌ Help command failed")
            return False
        
        print("✅ Help command works")
        
        # Test prediction command
        print("🔮 Testing prediction command with live data...")
        result = subprocess.run([sys.executable, "main.py", "--predict", "AAPL", "--timeframe", "30"], 
                              capture_output=True, text=True, timeout=120)
        
        if result.returncode != 0:
            print(f"❌ Prediction command failed")
            print(f"Error: {result.stderr}")
            return False
        
        print("✅ Prediction command works with live data")
        
        # Show sample output
        output_lines = result.stdout.split('\n')
        for i, line in enumerate(output_lines[:15]):
            if line.strip():
                print(f"   {line}")
        
        if len(output_lines) > 15:
            print("   ... (output truncated)")
        
        print("\n🎉 CLI interface test passed!")
        return True
        
    except Exception as e:
        print(f"❌ CLI interface test failed: {e}")
        return False

def test_multiple_predictions():
    """Test multiple predictions with different timeframes"""
    print_step(5, "Testing Multiple Predictions")
    
    try:
        from src.neural_network import NeuralNetworkPredictor
        
        predictor = NeuralNetworkPredictor()
        
        test_cases = [
            ('AAPL', 7),
            ('MSFT', 14),
            ('GOOGL', 30),
            ('TSLA', 60),
        ]
        
        for ticker, timeframe in test_cases:
            print(f"🔮 Testing {ticker} for {timeframe} days...")
            
            prediction = predictor.predict_stock_movement(ticker, timeframe)
            
            if 'error' in prediction:
                print(f"  ❌ Failed: {prediction['error']}")
                continue
            
            pred_label = prediction.get('prediction', 'N/A')
            confidence = prediction.get('confidence', 0)
            
            print(f"  ✅ {pred_label} ({confidence:.1f}% confidence)")
            
            time.sleep(1)  # Rate limiting
        
        print("\n🎉 Multiple predictions test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Multiple predictions test failed: {e}")
        return False

def test_system_performance():
    """Test system performance metrics"""
    print_step(6, "Testing System Performance")
    
    try:
        import time
        from src.neural_network import NeuralNetworkPredictor
        
        predictor = NeuralNetworkPredictor()
        
        # Test prediction speed
        print("⏱️ Testing prediction speed...")
        start_time = time.time()
        
        prediction = predictor.predict_stock_movement('AAPL', 30)
        
        end_time = time.time()
        prediction_time = end_time - start_time
        
        if 'error' not in prediction:
            print(f"✅ Prediction completed in {prediction_time:.2f} seconds")
            
            if prediction_time < 10:
                print("🚀 Excellent performance!")
            elif prediction_time < 30:
                print("✅ Good performance")
            else:
                print("⚠️ Slow performance (may need optimization)")
        else:
            print("❌ Performance test failed")
            return False
        
        # Test caching
        print("💾 Testing prediction caching...")
        start_time = time.time()
        
        # Same prediction should be cached
        cached_prediction = predictor.predict_stock_movement('AAPL', 30)
        
        end_time = time.time()
        cached_time = end_time - start_time
        
        print(f"✅ Cached prediction in {cached_time:.2f} seconds")
        
        if cached_time < prediction_time / 2:
            print("🚀 Caching working effectively!")
        
        print("\n🎉 Performance test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def run_complete_verification():
    """Run complete system verification"""
    print_header("STOCKTREK COMPLETE SYSTEM VERIFICATION")
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Live Market Data", test_live_market_data),
        ("Neural Network", test_neural_network),
        ("CLI Interface", test_cli_interface),
        ("Multiple Predictions", test_multiple_predictions),
        ("System Performance", test_system_performance),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name} - PASSED")
            else:
                print(f"\n❌ {test_name} - FAILED")
        except Exception as e:
            print(f"\n❌ {test_name} - FAILED with exception: {e}")
    
    # Final results
    print_header("VERIFICATION RESULTS")
    print(f"📊 Tests Passed: {passed}/{total}")
    print(f"🕐 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed == total:
        print("\n🎉 🎉 🎉 COMPLETE SUCCESS! 🎉 🎉 🎉")
        print("🚀 StockTrek is 100% READY with LIVE DATA!")
        print("\n📋 Production Commands:")
        print("   python3 main.py --predict AAPL --timeframe 30")
        print("   python3 main.py --daemon")
        print("   python3 main.py --backtest TSLA --days 90")
        print("\n💡 System is production-grade and fully tested!")
    else:
        print(f"\n⚠️ {total - passed} tests failed")
        print("Please fix issues before production use")
        print("Check SETUP_GUIDE.md for troubleshooting")
    
    return passed == total

def main():
    """Main verification function"""
    success = run_complete_verification()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
